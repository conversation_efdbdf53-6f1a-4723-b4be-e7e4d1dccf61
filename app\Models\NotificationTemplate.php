<?php

namespace App\Models;

use App\Traits\HasUuid;
use Illuminate\Database\Eloquent\Model;
use Astrotomic\Translatable\Contracts\Translatable as TranslatableContract;
use Astrotomic\Translatable\Translatable;

class NotificationTemplate extends Model implements TranslatableContract
{
    use HasUuid, Translatable;
    public $translatedAttributes = ['title', 'message'];
    protected $fillable = ['key', 'type', 'placeholders', 'is_active'];

}
