<?php

namespace App\Http\Controllers;

use App\Models\NotificationTemplate;
use App\Models\NotificationTemplateTranslation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class NotificationTemplateController extends Controller
{
    public function index()
    {
        $templates = NotificationTemplate::with('translations')->get();
        return view('dashboard.notification-templates.index', compact('templates'));
    }

    public function create()
    {
        return view('dashboard.notification-templates.create');
    }

    public function store(Request $request)
    {
        try{
            // $request->validate([
            //     'key' => 'required|unique:notification_templates,key',
            //     'translations.en.title' => 'required',
            //     'translations.en.message' => 'required',
            //     'translations.es.title' => 'required',
            //     'translations.es.message' => 'required',
            // ]);
            $request_data = $request->all();
            // remove spaces with underscore
            $key = str_replace(' ', '_', strtolower($request->key));
            // if (NotificationTemplate::where('key', $key)->exists()) {
            //     return "Key already exists";
            //     return redirect()->back()->with('message', 'Key already exists');
            // }
            $request_data['key'] = $key;
            $request_data['type'] = $request->type ?? 'custom';
            $request_data['is_active'] = true;
            $request_data['placeholders'] = json_encode($request->placeholders);
    
            $template = NotificationTemplate::create($request_data);
            // $template = NotificationTemplate::create([
            //     'key' =>  $key,
            //     'type' => $request->type ?? 'custom',
            //     'is_active' => true
            // ]);
    
            // foreach ($request->translations as $locale => $translation) {
            //     NotificationTemplateTranslation::create([
            //         'notification_template_id' => $template->id,
            //         'locale' => $locale,
            //         'title' => $translation['title'],
            //         'message' => $translation['message']
            //     ]);
            // }
        }catch(\Exception $e){
            DB::rollback();
            return $e->getMessage();
        }

        return redirect()->route('notification-templates.index')
            ->with('success', 'Notification template created successfully');
    }

    public function edit($id)
    {
        $template = NotificationTemplate::with('translations')->findOrFail($id);
        return view('dashboard.notification-templates.edit', compact('template'));
    }

    public function update(Request $request, $id)
    {
        $template = NotificationTemplate::findOrFail($id);
        
        $request->validate([
            'translations.en.title' => 'required',
            'translations.en.message' => 'required',
            'translations.es.title' => 'required',
            'translations.es.message' => 'required',
        ]);

        foreach ($request->translations as $locale => $translation) {
            NotificationTemplateTranslation::updateOrCreate(
                [
                    'notification_template_id' => $template->id,
                    'locale' => $locale
                ],
                [
                    'title' => $translation['title'],
                    'message' => $translation['message']
                ]
            );
        }

        return redirect()->route('notification-templates.index')
            ->with('success', 'Notification template updated successfully');
    }
}