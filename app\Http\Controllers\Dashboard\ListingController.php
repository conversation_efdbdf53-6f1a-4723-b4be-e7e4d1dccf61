<?php

namespace App\Http\Controllers\Dashboard;

use App\Amenity;
use App\AmenityOption;
use App\Booking;
use App\Category;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\ListingRequest;
use App\Listing;
use App\listingGallery;
use App\Models\User;
use App\Notifications\SuspendUserNotification;
use App\Services\BookingService;
use App\Services\ListingService;
use App\Services\SuspendService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use ZipArchive;

class ListingController extends Controller
{
    function __construct(
        protected ListingService $listingService,
        protected BookingService $bookingService,
        protected SuspendService $suspendService
    ) {
        $this->middleware('listingConsent')->only('store');
    }
    public function get_cateegory()
    {
        $categories = Category::get();
        return view("listing.listing.category", compact("categories"));
    }
    public function consentPost(Request $request)
    {
        User::where('id', auth()->user()->id)->update(['listing_consent' => 1]);
        return redirect()->route('get_category');
    }

    function listing_image_upload(Request $request)
    {
        if ($request->hasFile('images')) {
            $file = $request->file('images');
        } elseif ($request->hasFile('documents')) {
            $file = $request->file('documents');
        } else {
            return response()->json([
                "error" => "No file uploaded."
            ], 400);
        }

        return response()->json([
            "path" => $this->storeImage("listing-image", $file),
            "original_name" => $file->getClientOriginalName()
        ]);
    }
    function create_update(ListingRequest $request, $category_ids, $listing_id = null){
        $response = $this->listingService->create_update($request->validated(), $request->category_id);
        if (($response["status"] ?? null) == true) {
            return api_response(true, $response["message"], $response["data"] ?? null);
        } else {
            return api_response(false, translate('dashboard_listing.something_went_wrong_message'), null);
        }
    }
    public function listingMedia(Request $request)
    {
        $messages = [
            'media.required' => 'The media file is required.',
            'media.array' => 'The media file must be an array.',
            'media.*.required' => 'Each media file is required.',
            'media.*.mimes' => 'The media file must be a valid :type file.',
            'media.*.max' => 'The :attribute should not be greater than 10 MB.', // Default message for max size
        ];

        $type = $request->type;

        // $allowedMimes = $type === 'image' ? 'jpeg,png,jpg,heif,heic' : 'pdf,doc,docx,jpeg,png,jpg,heif,heic';
        $allowedMimes = $type === 'image' ? 'jpeg,png,jpg,heif,heic' : 'pdf,jpeg,png,jpg';

        $maxSize = $type === 'file' ? 10240 : 10240; // 3 MB for 'file' type, 10 MB for others
        $validator = Validator::make($request->all(), [
            'listing_id' => 'required|exists:listings,ids',
            'type' => 'required|in:image,file',
            'media' => 'required|array',
            'media.*' => [
                'required',
                'mimes:' . $allowedMimes,
                $type === 'image' ? 'min:300' : null, // no limit for file
                'max:' . $maxSize, // Set max size based on type (3 MB or 10 MB)
            ],
        ], [
            'media.*.mimes' => str_replace(':type', $type === 'image' ? 'image (jpeg, png, jpg, heif, heic)' : 'document (' . $allowedMimes . ')', $messages['media.*.mimes']),
            // 'media.*.mimetypes' => str_replace(':type', $type === 'image' ? 'image (jpeg, png, jpg, heif, heic)' : 'document (' . $allowedMimes . ')', $messages['media.*.mimes']),
            'media.*.max' => $type === 'file' ? 'The :attribute should not be greater than 10 MB.' : 'The :attribute should not be greater than 10 MB.', // Custom message based on file type
            'media.*.min' => 'The :attribute must be at least 300 KB.',
        ]);

        // Customize attribute names
        if ($request->hasFile('media')) {
            $customAttributes = [];
            foreach ($request->file('media') as $key => $file) {
                $customAttributes["media.$key"] = $file->getClientOriginalName();
            }
            $validator->setAttributeNames($customAttributes);
        }

        $validator->after(function ($validator) use ($request) {
            if ($request->hasFile('media')) {
                foreach ($request->file('media') as $key => $file) {
                    $fileName = $file->getClientOriginalName();
                    // Replace error messages
                    $errors = $validator->errors()->get("media.$key");
                    foreach ($errors as $message) {
                        $message = str_replace("10240 kilobytes", "10 MB", $message);
                        $message = str_replace("3072 kilobytes", "3 MB", $message);
                        $message = str_replace(":attribute", $fileName, $message);
                        // Re-add the formatted error message
                        $validator->errors()->add("media.$key", $message);
                    }
                }
            }
        });

        if ($validator->fails()) {
            return api_response(false, $validator->errors()->first());
        }

        $listing = Listing::where("ids", $request->listing_id)->firstOrFail();

        $existingMediaCount = listingGallery::where('listing_id', $listing->id)
            ->where("type", $request->type)
            ->count();

        $newMediaCount = is_array($request->media) ? count($request->media) : 0;
        $totalMediaCount = $existingMediaCount + $newMediaCount;
        if ($request->type == "image" && $totalMediaCount > 100) {
            return api_response(false, 'You can upload a maximum of 100 ' . $request->type . 's in total.');
        } elseif ($request->type == "file" && $totalMediaCount > 10) {
            return api_response(false, 'You can upload a maximum of 10 ' . $request->type . 's in total.');
        }

        $currentMaxSortOrder = listingGallery::where('listing_id', $listing->id)
            ->where("type", $request->type)
            ->max('sort_order') ?? 0;

        $isImageType = $request->type === "image";

        if ($isImageType) {
            $hasCover = listingGallery::where('listing_id', $listing->id)
                ->where('is_cover', 1)
                ->exists();
        }

        $mediaPaths = [];

        foreach ($request->media as $key => $media) {
            $listingGallery = new listingGallery();
            $listingGallery->listing_id = $listing->id;
            $listingGallery->name = $media->getClientOriginalName();
            $listingGallery->type = $request->type;
            $listingGallery->url = $this->storeImageToWebP("listing-" . $request->type, $media);
            $listingGallery->is_cover = ($isImageType && !$hasCover && $key === 0) ? 1 : 0;
            $listingGallery->sort_order = $isImageType ? $currentMaxSortOrder + $key + 1 : 0;
            $listingGallery->save();

            $mediaPaths[] = $listingGallery;
        }

        return api_response(true, ucfirst($request->type) . "s uploaded successfully", $mediaPaths);
    }
    function listing_image_delete(Request $request)
    {
        $validator = Validator::make($request->all(), [
            "listing_id" => "required|exists:listings,ids",
            "image_id" => "required|exists:listing_galleries,id",
        ], [
            "listing_id.exists" => "Listing does not exist",
            "image_id.exists" => "Image does not exist"
        ]);
        if ($validator->fails()) {
            return api_response(false, $validator->errors()->first());
        }
        try {
            $listing = Listing::where('ids', $request->listing_id)
                ->when(!auth()->user()->hasRole('user'), function ($query) {
                    $query->where('user_id', auth()->id());
                })->first();
            if (!$listing) {
                return api_response(false, "Listing not found");
            }
            $listing_gallery = listingGallery::where("id", $request->image_id)->where("listing_id", $listing->id)->firstOrFail();
            if (!$listing_gallery) {
                return api_response(false, "Image not found");
            }
            $this->deleteImage($listing_gallery->url);
            if ($listing_gallery->is_cover == 1) {

                $new_cover = listingGallery::where("listing_id", $listing->id)->where("is_cover", "!=", 1)->where('type', 'image')->orderBy("id", "ASC")->first();
                if (isset($new_cover)) {
                    $new_cover->is_cover = 1;
                    $new_cover->save();
                }
            }
            $listing_gallery->delete();
            return api_response(true, "Listing Image deleted successfully");
        } catch (\Exception $e) {
            return api_response(false, $e->getMessage());
        }
    }
    function listing_cover_image(Request $request)
    {
        try {
            $request->validate([
                "listing_id" => "required|exists:listings,ids",
                "image_id" => "required|exists:listing_galleries,id",
            ]);
            // $listing = Listing::where("user_id", auth()->id())
            //     ->where("ids", $request->listing_id)
            //     ->first();
            $listing = Listing::query();

            if (!auth()->user()->hasRole('user')) {
                $listing = $listing->where('user_id', auth()->id());
            }
            $listing = $listing->where("ids", $request->listing_id)->first();
            if (!$listing) {
                return api_response(false, "Listing not found");
            }
            $listing_gallery = listingGallery::where("id", $request->image_id)
                ->where("listing_id", $listing->id)
                ->first();

            if (!$listing_gallery) {
                return api_response(false, "Image not found");
            }
            $cover_image = listingGallery::where("is_cover", 1)
                ->where("listing_id", $listing->id)
                ->first();
            if ($cover_image) {
                $temp_sort_order = $cover_image->sort_order;
                $cover_image->is_cover = 0;
                $cover_image->sort_order = $listing_gallery->sort_order;
                $cover_image->save();

                $listing_gallery->is_cover = 1;
                $listing_gallery->sort_order = $temp_sort_order;
            } else {
                $listing_gallery->is_cover = 1;
            }
            $listing_gallery->save();
            return api_response(true, "Listing Image set as cover successfully");
        } catch (\Exception $e) {
            return api_response(false, $e->getMessage());
        }
    }
    function listing_photo_order(Request $request)
    {
        $imageIds = $request->input('image_ids');
        $listingID = $request->input('listing_id');
        $listing = Listing::where("ids", $listingID)->firstOrFail();
        if (!$imageIds || !$listingID) {
            return response()->json(['status' => false, 'message' => 'Invalid data']);
        }

        foreach ($imageIds as $index => $id) {
            $listing->gallery_images()
                ->where('id', $id)
                ->update(['sort_order' => $index + 1]);
        }
        return response()->json(['status' => true, 'message' => 'Sort order updated successfully']);
    }
    public function listing_add($slug, $listing_ids = null)
    {
        $category = Category::with("cms_steppers", "amenities.options")->where('slug', $slug)->firstOrFail();
        $listing = $listing_ids ? Listing::where('ids', $listing_ids)->first() : null;
        if ($listing_ids && !$listing) {
            abort(404, 'Listing not found');
        }
        return view('listing.listing.create', compact('listing', 'slug', 'category'));
    }
    public function duplicate_listing($slug, $listing_ids)
    {
        // try{
            $duplicate_listing = $this->listingService->duplicate($listing_ids);
            return redirect()->route("listing_add", ["slug" => $duplicate_listing->category->slug, "listing_ids" => $duplicate_listing->ids]);
        // }catch(\Exception $e){
        //     return redirect()->back()->with("error", $e->getMessage());
        // }
    }
    function listing_store(Request $request, ListingService $listing)
    {
        return $request->all();
        $this->add_category_list($request->all(), $request->category_id, $request->listing_id, $listing);
        return "listing create";
    }
    function get_amenity_option(Request $request)
    {
        $request->validate([
            "category" => "required",
            "amenity_options" => "array",
        ]);
        $select_options = $request->amenity_options ?? [];
        // $amenity_options = AmenityOption::with("amenity")->whereIn("id", $select_options)->get();
        $categoryId = $request->category;
        $category = Category::with("amenities.options")->where('id', $categoryId)->firstOrFail();
        // return $category;
        $amenity_options = AmenityOption::with('amenity')
            ->whereIn('id', $select_options) // Filter based on select_options
            ->whereHas('amenity', function ($query) use ($categoryId) {
                $query->whereHas('categories', function ($subQuery) use ($categoryId) {
                    $subQuery->where('category_id', $categoryId); // Filter by category_id in pivot table
                });
            })
            ->get();
        // return $amenity_options;
        $data = (string) view("listing.listing.layout.amenity-box", compact("select_options", "amenity_options", "category"));
        return api_response(true, "data found", $data);
    }
    public function addTour()
    {
        return view('listing.listing.forms.add_tour');
    }

    function add_category_list($request_data, $category_id, $listing_id, $listing)
    {
        $category = Category::find($category_id);
        $category_name = $category->common_name ?? "";
        if (isset($category)) {
            $update_listing = Listing::find($listing_id);
            $message = "";
            if (isset($update_listing)) {
                $res = $listing->update_listing($request_data, $update_listing->id, $category->id);
                $message = "$category_name updated successfully";
            } else {
                $res = $listing->add_listing($request_data, $category->id);
                $message = "$category_name added successfully";
            }
            if ($res["status"] == true) {
                return ["status" => true, "message" => $message];
            } else {
                return ["status" => false, "message" => "Something went wrong"];
            }
        } else {
            return ["status" => false, "message" => "Category not found"];
        }
    }
    function reject_listing(Request $request)
    {
        $request->validate([
            "listing_id" => "required",
        ]);
        $listing = Listing::find($request->listing_id);
        if ($listing) {
            $listing->status = 2;
            $listing->reason = $request->reason;
            $listing->save();
            return back()->with('flash_message', 'Listing Rejected!');
        }
    }
    function approve_listing($id)
    {
        if (auth()->user()->hasRole(["user", "sub_admin"])) {
            $listing = Listing::find($id);
            if ($listing) {
                $listing->status = 1;
                $listing->save();
                return back()->with('flash_message', 'Listing Approved!');
            }
        }
    }
    function suspended_listing($id)
    {
        $listing = Listing::find($id);
        if (auth()->user()->hasRole(['user', "sub_admin"])) {
            if ($listing) {
                if ($listing->status == 5) {
                    $listing->status = 1;
                    $listing->save();
                    return api_response(true, "Listing unsuspended successfully");
                } else {
                    $active_bookings = Booking::where("status", 0)->where("listing_id", $listing->id)->get();
                    foreach ($active_bookings as $active_booking) {
                        // user Notifications
                        $active_booking->customer->notify(new SuspendUserNotification($active_booking));
                        $active_booking->status = 5;
                        $active_booking->save();
                    }
                    $listing->status = 5;
                    $listing->save();
                    return api_response(true, "Listing suspended successfully");
                }
            } else {
                return api_response(false, "Listing not Found", 0);
            }
        }
    }
    function bulk_delete(Request $request)
    {
        $request->validate([
            "listing_ids" => "required|array",
        ]);
        if (auth()->user()->hasRole("user")) {
            $listings = Listing::whereIn("ids", $request->listing_ids)->get();
        } else {
            $listings = Listing::where("user_id", auth()->id())->get();
        }
        if ($listings->isEmpty()) {
            return redirect()->back()->with('message', 'No listing found');
        }
        foreach ($listings as $listing) {
            $this->listingService->deleteListing($listing->id);
        }
        return redirect()->back()->with('message', 'Listing deleted successfully');
    }

    function listing_booking(Request $request)
    {
        $request->validate([
            "listing_ids" => "required",
        ]);
        $listings = $this->listingService->multiple_listing($request->listing_ids);
        $data = (string) view("includes.active-booking", compact("listings"));
        return api_response(true, "Listin data found", $data);
    }
    function listing_pause($id)
    {
        $listing = $this->listingService->pauseListing($id);
        return back()->with('message', $listing["message"]);
    }
    function listing_active_booking(Request $request, $id)
    {
        $listing = $this->listingService->singleListing($id);
        if (!$listing) {
            return api_response(false, "Listing not found");
        }
        $active_bookings = $this->bookingService->activeBookings('listing', $listing->id);
        if ($request->type == "delete") {
            $active_booking_table = (string) view("includes.delete-listing-form", compact('active_bookings', "listing"));
        } else {
            $active_booking_table = (string) view("includes.suspend-modal-table", compact('active_bookings', "listing"));
        }
        return api_response(true, "Data found", $active_booking_table);
    }
    function get_listing_documents($listing_ids)
    {
        if (auth()->user()->hasRole("user")) {
            $listing = Listing::where("ids", $listing_ids)->firstOrFail();
        } else {
            $listing = Listing::where("ids", $listing_ids)->where("user_id", auth()->id())->firstOrFail();
        }
        return api_response(true, "File found", $listing->files);
    }

    public function downloadListingAssetsZip($listingId)
    {
        try {
            $listing = Listing::where('ids', $listingId)->first();
            $files = $listing->files;
            if ($files->isEmpty()) {
                return response()->json([
                    'status' => false,
                    'message' => 'No files found for this listing',
                ], 404);
            }

            $zip = new ZipArchive;
            $zipFileName = $listing->slug . '.zip';
            $zipPath = storage_path('app/temp/' . $zipFileName);
            if (!file_exists(storage_path('app/temp'))) {
                Storage::disk('local')->makeDirectory('temp');
            }
            if ($zip->open($zipPath, ZipArchive::CREATE | ZipArchive::OVERWRITE) === TRUE) {
                foreach ($files as $file) {
                    $filePath = public_path("website") . "/" . $file->url;
                    $fileName = $file->name;
                    $zip->addFile($filePath, $fileName);
                }
                $zip->close();
                return response()->download($zipPath)->deleteFileAfterSend(true);
            } else {
                return response()->json([
                    'status' => false,
                    'message' => 'Failed to create zip file',
                ], 500);
            }
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Something went wrong: ' . $e->getMessage(),
            ], 500);
        }
    }
    function short_generate_link($listing_id)
    {
        $listing = Listing::where("ids", $listing_id)->first();
        return $this->listingService->url_code($listing_id);
    }
    function internal_name(Request $request)
    {
        $request->validate([
            "listing_ids" => "required",
            // "internal_name" => "required"
        ]);
        $listing = Listing::where("ids", $request->listing_ids)->first();
        $listing->internal_name = $request->internal_name;
        $listing->save();
        return back()->with(["message" => "Internal name saved", "title" => "Success", "type" => "success"]);
    }
}
