    @php
    if ($category->id == 1) {
        $listing_img = asset('website') . '/images/experience.png';
    } elseif ($category->id == 2) {
        $listing_img = asset('website') . '/images/stepper_boat.png';
    } elseif ($category->id == 3) {
        $listing_img = asset('website') . '/images/stepper_car.png';
    } elseif ($category->id == 4) {
        $listing_img = asset('website') . '/images/book_details.png';
    }
@endphp
<div id="sec-loader">
    <div class="load">
        <img src="{{ asset('website') }}/images/luxustar-loader.gif" alt="Loader">
    </div>
</div>
<div class="container-fluid p-0">
    <input type="hidden" name="listing_id" value="{{ (isset($isDuplication) && $isDuplication) ? '' : ($listing->ids ?? '') }}">
    <input type="hidden" name="category_id" value="{{ $category->id ?? '' }}">
    @if(isset($isDuplication) && $isDuplication)
        <input type="hidden" name="is_duplication" value="1">
        <input type="hidden" name="original_listing_id" value="{{ $originalListingId ?? '' }}">
    @endif
    <div class="row">
        <div class="col-lg-12 p-0">
            <div class="save-header d-flex">
                <a href="{{ url('/') }}">
                    <img class="logo" src="{{ asset('/') . App\CommonSetting::first()->dashboard_logo }}"alt="Logo">
                    <img class="favicon" src="{{ asset('/') . App\CommonSetting::first()->favicon }}"alt="Favicon">
                </a>
                <span>
                    <button type="button" data-status="6"
                        class="save-btn save_and_exit_btn">{{ translate('stepper.save_exit') }}</button>
                </span>
            </div>
        </div>

        <div class="col-lg-12">
            <div class="cust_progress">
                <div class="cust_progress_line"></div>
                <div class="cust_progress_bar "></div>
            </div>
        </div>
    </div>
</div>

{{-- Experiences (Tour) --}}
@if ($category->id == 1)
    @include('listing.listing.steps.experiences.step_1')
    @include('listing.listing.steps.experiences.step_2')
    @include('listing.listing.steps.experiences.step_3')

    {{-- Location Steps --}}
    @include('listing.listing.steps.experiences.step_4')
    @include('listing.listing.steps.experiences.step_5')

    @include('listing.listing.steps.experiences.step_6')
    @include('listing.listing.steps.experiences.step_7')

    @include('listing.listing.steps.experiences.step_27')

    {{-- Step 2 --}}
    @include('listing.listing.steps.experiences.step_8')
    @include('listing.listing.steps.experiences.step_9')
    @include('listing.listing.steps.experiences.step_10')
    @include('listing.listing.steps.experiences.step_11')

    {{-- Add images step --}}
    @include('listing.listing.steps.experiences.step_12')
    @include('listing.listing.steps.experiences.step_13')

    {{-- Title --}}
    @include('listing.listing.steps.experiences.step_14')
    @include('listing.listing.steps.experiences.step_15')

    {{-- File upload step --}}
    @include('listing.listing.steps.experiences.step_28')

    {{-- Steps 3 --}}
    @include('listing.listing.steps.experiences.step_16')

    {{-- Price --}}
    @include('listing.listing.steps.experiences.step_17')

    {{-- Cancelation --}}
    @include('listing.listing.steps.experiences.step_18')
    @include('listing.listing.steps.experiences.step_19')
    @include('listing.listing.steps.experiences.step_19_children')
    @include('listing.listing.steps.experiences.step_19_privateBooking')
    @include('listing.listing.steps.experiences.step_20')
    @include('listing.listing.steps.experiences.step_21')

    {{-- Seasonal Pricing --}}
    {{-- @include('listing.listing.steps.experiences.step_22') --}}
    @include('listing.listing.steps.experiences.step_23')
    @include('listing.listing.steps.experiences.step_24')
    @include('listing.listing.steps.experiences.step_25')
    @include('listing.listing.steps.experiences.step_26')
@endif

{{-- Watercraft --}}
@if ($category->id == 2)
    @include('listing.listing.steps.watercraft.step_1')
    @include('listing.listing.steps.watercraft.step_2')
    @include('listing.listing.steps.watercraft.step_3')

    {{-- Location Steps --}}
    @include('listing.listing.steps.watercraft.step_4')
    @include('listing.listing.steps.watercraft.step_5')

    @include('listing.listing.steps.watercraft.step_6')

    {{-- Step 2 --}}
    @include('listing.listing.steps.watercraft.step_7')

    {{-- Commented due to an error (Gonna uncomment it once resolved --}}
    @include('listing.listing.steps.watercraft.step_8')

    @include('listing.listing.steps.watercraft.step_9')
    @include('listing.listing.steps.watercraft.step_10')

    {{-- Add images step --}}

    @include('listing.listing.steps.watercraft.step_12')

    @include('listing.listing.steps.watercraft.step_11')

    {{-- Title --}}
    @include('listing.listing.steps.watercraft.step_13')
    @include('listing.listing.steps.watercraft.step_14')

    {{-- Steps 3 --}}
    @include('listing.listing.steps.watercraft.step_15')

    {{-- Price --}}
    @include('listing.listing.steps.watercraft.step_16')

    {{-- Cancelation --}}
    @include('listing.listing.steps.watercraft.step_17')
    @include('listing.listing.steps.watercraft.step_18')
    @include('listing.listing.steps.watercraft.step_19')
    @include('listing.listing.steps.watercraft.step_20')

    {{-- Seasonal Pricing --}}
    {{-- @include('listing.listing.steps.watercraft.step_21') --}}
    @include('listing.listing.steps.watercraft.step_22')
    @include('listing.listing.steps.watercraft.step_23')

    @include('listing.listing.steps.watercraft.step_24')
    @include('listing.listing.steps.watercraft.step_25')
    @include('listing.listing.steps.watercraft.step_26')
    @include('listing.listing.steps.watercraft.step_27')
@endif

{{-- Car --}}
@if ($category->id == 3)
    {{-- Steps 1 --}}
    @include('listing.listing.steps.car.step_1')
    @include('listing.listing.steps.car.step_2')
    @include('listing.listing.steps.car.step_3')

    {{-- Location Steps --}}
    @include('listing.listing.steps.car.step_4')
    @include('listing.listing.steps.car.step_5')

    @include('listing.listing.steps.car.step_6')

    {{-- Steps 2 --}}
    @include('listing.listing.steps.car.step_7')
    @include('listing.listing.steps.car.step_8')
    @include('listing.listing.steps.car.step_9')


    {{-- REMOVED ON PURPOSE --}}
    {{-- @include('listing.listing.steps.car.step_10') --}}



    @include('listing.listing.steps.car.step_11')
    @include('listing.listing.steps.car.step_12')

    {{-- Add Photos --}}
    @include('listing.listing.steps.car.step_13')
    @include('listing.listing.steps.car.step_14')
    {{-- Title --}}
    @include('listing.listing.steps.car.step_15')
    @include('listing.listing.steps.car.step_16')

    {{-- Steps 3 --}}
    @include('listing.listing.steps.car.step_17')
    {{-- Price --}}
    @include('listing.listing.steps.car.step_18')

    {{-- Cancelation --}}
    @include('listing.listing.steps.car.step_19')
    @include('listing.listing.steps.car.step_22')
    @include('listing.listing.steps.car.step_20')



    @include('listing.listing.steps.car.step_21')




    {{-- Seasonal Pricing --}}
    @include('listing.listing.steps.car.step_23')

    {{-- REMOVED ON PURPOSE --}}
    {{-- @include('listing.listing.steps.car.step_24') --}}

    @include('listing.listing.steps.car.step_25')
    @include('listing.listing.steps.car.step_26')
    @include('listing.listing.steps.car.step_27')
    @include('listing.listing.steps.car.step_28')
    @include('listing.listing.steps.car.step_29')
    @include('listing.listing.steps.car.step_30')
@endif

{{-- Accommodation  --}}
@if ($category->id == 4)
    {{-- Steps 1 --}}
    @include('listing.listing.steps.accomodation.step_1')
    @include('listing.listing.steps.accomodation.step_2')
    @include('listing.listing.steps.accomodation.step_3')

    {{-- Location Steps --}}
    @include('listing.listing.steps.accomodation.step_4')
    @include('listing.listing.steps.accomodation.step_5')

    @include('listing.listing.steps.accomodation.step_6')

    {{-- Steps 2 --}}
    @include('listing.listing.steps.accomodation.step_7')
    @include('listing.listing.steps.accomodation.step_8')

    {{-- Step for custom amenities --}}
    {{-- @include('listing.listing.steps.accomodation.step_27') --}}

    @include('listing.listing.steps.accomodation.step_9')
    @include('listing.listing.steps.accomodation.step_10')
    @include('listing.listing.steps.accomodation.step_11')

    {{-- Add images step --}}
    @include('listing.listing.steps.accomodation.step_12')
    {{-- @include('listing.listing.steps.accomodation.step_13')   // step commented --}}
    @include('listing.listing.steps.accomodation.step_14')

    {{-- Title --}}
    @include('listing.listing.steps.accomodation.step_15')
    @include('listing.listing.steps.accomodation.step_16')

    {{-- Steps 3 --}}
    @include('listing.listing.steps.accomodation.step_17')
    {{-- Price --}}
    @include('listing.listing.steps.accomodation.step_18')

    {{-- Cancelation --}}
    @include('listing.listing.steps.accomodation.step_19')
    @include('listing.listing.steps.accomodation.step_20')
    @include('listing.listing.steps.accomodation.step_21')
    @include('listing.listing.steps.accomodation.step_22')

    {{-- Seasonal Pricing --}}
    @include('listing.listing.steps.accomodation.step_23')
    @include('listing.listing.steps.accomodation.step_24')
    @include('listing.listing.steps.accomodation.step_25')
    @include('listing.listing.steps.accomodation.step_26')
@endif


{{-- <div class="container-fluid"> --}}
{{-- <div class="row custom-rw"> --}}
{{-- <div class="col-md-12 custom-fieldset"> --}}

{{-- =================== step 1  =================== --}}
{{-- step 1 (Tell us about your listing) --}}
{{-- @include('listing.listing.steps.step1-1') --}}
{{-- step 1.3 (Where’s your located?)   --}}
{{-- @include('listing.listing.steps.step1-2') --}}
{{-- step 1.3 (Where’s your located?) end --}}
{{-- step 1 (Tell us about your listing) end  --}}
{{-- step 1.2 (confirm your address)   --}}
{{-- @include('listing.listing.steps.step1-3') --}}
{{-- step 1.2 (confirm your address) end --}}
{{-- step 1.4 (some basics about) --}}
{{-- @include('listing.listing.steps.step1-4', $category) --}}
{{-- step 1.4 (some basics about) end --}}
{{-- =================== step 1 end =================== --}}
{{-- =================== step 2  =================== --}}
{{-- @include('listing.listing.steps.step2-1') --}}
{{-- step 2.2 --}}
{{-- @include('listing.listing.steps.step2-2') --}}
{{-- step 2.2 end --}}

{{-- rule --}}
{{-- @include('listing.listing.steps.step2-3') --}}
{{-- rule end --}}

{{-- photo and document upload --}}
{{-- @include('listing.listing.steps.step2-4') --}}
{{-- photo and document upload --}}

{{-- title --}}
{{-- @include('listing.listing.steps.step2-5') --}}
{{-- title end --}}

{{-- description --}}
{{-- @include('listing.listing.steps.step2-6') --}}
{{-- description end --}}

{{-- advance your listing --}}
{{-- @include('listing.listing.steps.step2-7') --}}
{{-- advance your listing end --}}

{{-- step 3 --}}
{{-- @include('listing.listing.steps.step3-1') --}}
{{-- step 3 end --}}

{{-- price --}}
{{-- @include('listing.listing.steps.step3-2') --}}
{{-- price end --}}

{{-- discount --}}
{{-- @include('listing.listing.steps.step3-3') --}}
{{-- discount end --}}

{{-- review --}}
{{-- @include('listing.listing.steps.step3-4') --}}
{{-- review end --}}
{{-- </div> --}}
{{-- </div> --}}
{{-- </div> --}}

@php
    $review_step = $category?->cms_steppers->firstWhere('step_name', "review");
@endphp
@push('js')
    <script src="{{ asset('plugins/components/bootstrap-tagsinput/dist/bootstrap-tagsinput.min.js') }}"></script>
    <script>
        $(document).ready(function() {
            $('input[type="number"]').each(function() {
                const $input = $(this);
                $input.on('input', function() {
                    this.value = this.value.replace(/[^0-9]/g, '');
                });
                $input.on('keypress', function(event) {
                    if (!/[0-9]/.test(event.key)) {
                        event.preventDefault();
                    }
                });
                if (!$input.attr('min')) {
                    $input.attr('min', '0');
                }
            });
        });
    </script>

    <script>
        $(document).ready(function() {
            const review_title = "{{ $review_step->title ?? '' }}";
            const review_subtitle = "{{ $review_step->sub_title ?? '' }}";
            // Get the current URL
            let url = window.location.href;
            let listing_id = @json($listing->ids ?? null) || url.substring(url.lastIndexOf('/') + 1) ;
            console.log("listing_id:: ", listing_id);
            

            window.success = true;

            @if (!isset($listing))
                window.editListing = false;
                console.log("Inside if");
            @elseif (($listing->status ?? null) == 6 && $listing->is_duplication == false)
                window.editListing = false;
                console.log("Inside first elseif");
            @elseif ($listing->is_duplication == true)
                window.editListing = true;
                console.log("Duplicate Listing");
                console.log("Inside second elseif");
            @else
                window.editListing = true;
                console.log("Inside else");
            @endif



            console.log("Edit Listing: " + window.editListing);
            

            @if (isset($isDuplication))
                let current_step = 1;
                window.currentStep = 1;
            @elseif (isset($listing->step_no))
                let current_step = @js($listing->step_no);
                window.currentStep = @js($listing->step_no);
            @else
                let current_step = 0;
                window.currentStep = 1;
            @endif
            @if (!isset($listing))
                $(document).on("click", ".next", function(event) {
                    event.preventDefault();
                    let form_data = $("form").serializeArray();
                    current_step = $(this).parent().data("step-no");
                    let $button = $(this);
                    $button.html(@json(translate('stepper.loading')));
                    $button.prop("disabled", true);

                    let last_prev_step = current_step;

                    $(".cust_progress_bar").each(function() {
                        let $el = $(this);
                        let step_no = $el.data("step");

                        // if we hit the final_next_btn, stop checking further
                        if ($el.hasClass("final_next_btn")) {
                            return false; // break out of loop
                        }

                        // update only if it's marked as prev and greater than current
                        if ($el.hasClass("prev") && step_no > current_step) {
                            last_prev_step = step_no;
                        }
                    });

                    form_data.push({
                        name: "current_step",
                        value: last_prev_step
                    });

                    form_data.push({
                        name: "listing_status",
                        value: 6
                    });
                    if (current_step) {
                        $.ajax({
                            url: `{{ route('create_update', ["category_ids" => $category->ids]) }}/${listing_id}`,
                            type: "POST",
                            headers: {
                                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                            },
                            data: form_data,
                            success: function(response) {
                                if (response.status) {
                                    if (response.data.ids) {
                                        $('input[name="listing_id"]').val(response.data.ids);
                                        console.log("ids ", response.data.ids);
                                        console.log(response.message);
                                        var value = response.data.ids;
                                        var newUrl =
                                            `${window.location.protocol}//${window.location.host}/listings/{{ str_slug($category->display_name) }}/${encodeURIComponent(value)}`;
                                        history.pushState(null, '', newUrl);
                                        window.success = true;
                                        listing_id = response.data.ids;
                                        // console.log("Success Status ajax: " + success);
                                    } else {
                                        window.success = false;
                                    }
                                } else {
                                    Swal.fire({
                                        title: @json(translate('stepper.error')),
                                        text: response.message,
                                        icon: "error"
                                    });
                                    window.success = false;
                                }
                                // else {
                                //     alert("Something went wrong");
                                // }
                                console.log("response", response);
                            },
                            error: function(xhr) {
                                // alert("Error");
                                console.log("Error", xhr.responseText);
                                window.success = false;
                            },
                            complete: function() {
                                $button.html(@json(translate('stepper.next')));
                                $button.prop("disabled",
                                    false); // Reset only the clicked button
                            }
                        });
                    }
                });
            @endif


            $(document).on("click", ".final_next_btn", function() {
                let form_data = $("form").serializeArray();
                let $button = $(this);

                if($(this).parent('fieldset').length){
                    current_step = $(this).parent().data("step-no");
                }else{
                    current_step = $(this).data("step") - 1;
                }
                
                form_data.push({
                    name: "current_step",
                    value: current_step
                });
                form_data.push({
                    name: "listing_status",
                    value: 6
                });
                $.ajax({
                    url: "{{ route('listing.store') }}",
                    type: "POST",
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    data: form_data,
                    success: function(response) {
                        if (response.status) {
                            // Set the listing_id if it was created/updated and not already set
                            if (response.data && response.data.ids && !$('input[name="listing_id"]').val()) {
                                $('input[name="listing_id"]').val(response.data.ids);
                                console.log("Listing ID set after final step: ", response.data.ids);
                            }

                            $('.review_details_step .inner_section_fieldset').html(
                                `<div id="list_preview_loader"> <div class="inner_section_col"> <div class="main_step_title"> <h2>${review_title}</h2> </div> <div class="step_description"> <p>${review_subtitle}</p> </div> </div> <div class="load"> <img src="{{ asset('website') }}/images/luxustar-loader.gif" alt="Loader"> </div> <p>` + @json(translate('stepper.generating_preview')) + `</p> </div>`
                            );
                            var id = $('input[name="listing_id"]').val();
                            var category = 3;
                            $.ajax({
                                type: 'GET',
                                url: `{{ url('detail-slide') }}/` + id + `/` +
                                    category,
                                data: {
                                    id: id,
                                    category: category
                                },
                                success: function(data) {
                                    setTimeout(() => {
                                        if (data) {
                                            $(".review_details_step .inner_section_fieldset")
                                                .html(data);
                                        } else {
                                            console.log(
                                                'Error: No view received.'
                                                );
                                        }
                                    }, 4500);
                                },
                                error: function(xhr, ajaxOptions, thrownError) {
                                    swal(@json(translate('stepper.error_exclamation')), @json(translate('stepper.invalid_view')));
                                }
                            });
                        } else {
                            Swal.fire({
                                title: @json(translate('stepper.error')),
                                text: response.message,
                                icon: "error"
                            });
                            window.success = false;
                        }
                    },
                    error: function(xhr) {
                        console.log("Error", xhr.responseText);
                        window.success = false;
                    },
                    complete: function() {
                        // $button.html("Next");
                        // $button.prop("disabled", false);
                    }
                });
            });

            $(document).on("click", ".save_and_exit_btn", function() {
                let status = $(this).data("status");
                let form_data = $("form").serializeArray();
                let list_status = {{ $listing->status ?? '6' }};
                console.log(list_status);


                let last_prev_step = current_step;

                $(".cust_progress_bar").each(function() {
                    let $el = $(this);
                    let step_no = $el.data("step");

                    if ($el.hasClass("final_next_btn")) {
                        return false;
                    }

                    if ($el.hasClass("prev") && step_no > current_step) {
                        last_prev_step = step_no;
                    }
                });

                form_data.push({
                    name: "current_step",
                    value: last_prev_step
                });


                // form_data.push({
                //     name: "current_step",
                //     value: current_step
                // });

                form_data.push({
                    name: "listing_status",
                    value: status
                });

                if($('input[type="hidden"][name="category_id"]').val() == 1){
                    var child_min_age = $('#child_age_from').val();
                    var child_max_age = $('#child_age_to').val();
                }

                console.log(`Current Step: ${current_step}`);

                var clickedBtn = $(this);

                // $(document).on('submit', 'form', function(event) {
                    // event.preventDefault();
                    var priceFilled = true;
                    if($('input[type="hidden"][name="category_id"]').val() == 1 && !clickedBtn.hasClass('save-btn')){
                        var childSelectedValue = $('input[name="child_allow"]:checked').val();
                        var privateBookingSelectedValue = $('input[name="private_booking"]:checked').val();
                        if(childSelectedValue == "no" && privateBookingSelectedValue == "no"){
                            var priceFilled = true;
                        }else if (childSelectedValue == "yes" && privateBookingSelectedValue == "no"){
                            var child_price = $('.hidden_price_children').val();
                            if(child_price == '' || child_min_age == '' || child_max_age == ''){

                                if(child_price != '' && (child_min_age == '' || child_max_age == '')){
                                    Swal.fire({
                                        icon: "error",
                                        title: @json(translate('stepper.invalid_age')),
                                        text: @json(translate('stepper.children_min_max_age')),
                                    });
                                    priceFilled = false;
                                }else if (child_price == '' && (child_min_age != '' && child_max_age != '')){
                                    Swal.fire({
                                        icon: "error",
                                        title: @json(translate('stepper.invalid_price')),
                                        text: @json(translate('stepper.price_children')),
                                    });
                                    priceFilled = false;
                                }else{
                                    Swal.fire({
                                        icon: "error",
                                        title: @json(translate('stepper.invalid_price_age')),
                                        text: @json(translate('stepper.children_min_max_age_price')),
                                    });
                                    priceFilled = false;
                                }
                                
                            }else{
                                priceFilled = true;
                            }
                        }else if (childSelectedValue == "no" && privateBookingSelectedValue == "yes"){
                        var child_price = $('.hidden_price_private_booking').val();
                        if(child_price == ''){
                            Swal.fire({
                                icon: "error",
                                title: @json(translate('stepper.invalid_price')),
                                text: @json(translate('stepper.price_private')),
                            });
                            priceFilled = false;
                        }else{
                            priceFilled = true;
                        }
                        }else if (childSelectedValue == "yes" && privateBookingSelectedValue == "yes"){
                            var child_price = $('.hidden_price_children').val();
                            var private_booking_price = $('.hidden_price_private_booking').val();

                            if(child_price == '' || child_min_age == '' || child_max_age == '' || private_booking_price == ''){

                                let missingFields = [];

                                const isPrivatePriceMissing = private_booking_price === '';
                                const isChildPriceMissing = child_price === '';
                                const isMinAgeMissing = child_min_age === '';
                                const isMaxAgeMissing = child_max_age === '';

                                const isAnyPriceMissing = isPrivatePriceMissing || isChildPriceMissing;
                                const isAnyAgeMissing = isMinAgeMissing || isMaxAgeMissing;

                                if (isPrivatePriceMissing && isChildPriceMissing && isMinAgeMissing && isMaxAgeMissing) {
                                    Swal.fire({
                                        icon: "error",
                                        title: "Invalid Price and Age",
                                        text: "Please enter a price for children and private booking & age for children.",
                                    });
                                    priceFilled = false;
                                } else if (isAnyPriceMissing && !isAnyAgeMissing) {
                                    let missingPrice = [];
                                    if (isPrivatePriceMissing) missingPrice.push("private booking price");
                                    if (isChildPriceMissing) missingPrice.push("children price");

                                    Swal.fire({
                                        icon: "error",
                                        title: "Invalid Price",
                                        text: `Please enter ${missingPrice.join(" and ")}.`,
                                    });
                                    priceFilled = false;
                                } else if (!isAnyPriceMissing && isAnyAgeMissing) {
                                    let missingAge = [];
                                    if (isMinAgeMissing) missingAge.push("minimum age");
                                    if (isMaxAgeMissing) missingAge.push("maximum age");

                                    Swal.fire({
                                        icon: "error",
                                        title: "Invalid Age",
                                        text: `Please enter ${missingAge.join(" and ")} for children.`,
                                    });
                                    priceFilled = false;
                                } else if (isAnyPriceMissing && isAnyAgeMissing) {
                                    let missingPrice = [];
                                    if (isPrivatePriceMissing) missingPrice.push("private booking price");
                                    if (isChildPriceMissing) missingPrice.push("children price");

                                    let missingAge = [];
                                    if (isMinAgeMissing) missingAge.push("minimum age");
                                    if (isMaxAgeMissing) missingAge.push("maximum age");

                                    Swal.fire({
                                        icon: "error",
                                        title: "Invalid Price and Age",
                                        text: `Please enter ${missingPrice.join(" and ")} & ${missingAge.join(" and ")} for children.`,
                                    });
                                    priceFilled = false;
                                }
                                
                            }else{
                                priceFilled = true;
                            }
                        }
                    }else if ((list_status == 1 || list_status == 0) && $('input[type="hidden"][name="category_id"]').val() == 1 && clickedBtn.hasClass('save-btn')) {
                        var childSelectedValue = $('input[name="child_allow"]:checked').val();
                        var privateBookingSelectedValue = $('input[name="private_booking"]:checked').val();
                        if(childSelectedValue == "no" && privateBookingSelectedValue == "no"){
                            var priceFilled = true;
                        }else if (childSelectedValue == "yes" && privateBookingSelectedValue == "no"){
                            var child_price = $('.hidden_price_children').val();
                            if(child_price == '' || child_min_age == '' || child_max_age == ''){

                                if(child_price != '' && (child_min_age == '' || child_max_age == '')){
                                    Swal.fire({
                                        icon: "error",
                                        title: @json(translate('stepper.invalid_age')),
                                        text: @json(translate('stepper.children_min_max_age')),
                                    });
                                    priceFilled = false;
                                }else if (child_price == '' && (child_min_age != '' && child_max_age != '')){
                                    Swal.fire({
                                        icon: "error",
                                        title: @json(translate('stepper.invalid_price')),
                                        text: @json(translate('stepper.price_children')),
                                    });
                                    priceFilled = false;
                                }else{
                                    Swal.fire({
                                        icon: "error",
                                        title: @json(translate('stepper.invalid_price_age')),
                                        text: @json(translate('stepper.children_min_max_age_price')),
                                    });
                                    priceFilled = false;
                                }

                            }else{
                                priceFilled = true;
                            }
                        }else if (childSelectedValue == "no" && privateBookingSelectedValue == "yes"){
                            var child_price = $('.hidden_price_private_booking').val();
                            if(child_price == ''){
                                Swal.fire({
                                    icon: "error",
                                    title: @json(translate('stepper.invalid_price')),
                                    text: @json(translate('stepper.price_private')),
                                });
                                priceFilled = false;
                            }else{
                                priceFilled = true;
                            }
                        }else if (childSelectedValue == "yes" && privateBookingSelectedValue == "yes"){
                            var child_price = $('.hidden_price_children').val();
                            var private_booking_price = $('.hidden_price_private_booking').val();
                            if(child_price == '' || child_min_age == '' || child_max_age == '' || private_booking_price == ''){

                                let missingFields = [];

                                const isPrivatePriceMissing = private_booking_price === '';
                                const isChildPriceMissing = child_price === '';
                                const isMinAgeMissing = child_min_age === '';
                                const isMaxAgeMissing = child_max_age === '';

                                const isAnyPriceMissing = isPrivatePriceMissing || isChildPriceMissing;
                                const isAnyAgeMissing = isMinAgeMissing || isMaxAgeMissing;

                                if (isPrivatePriceMissing && isChildPriceMissing && isMinAgeMissing && isMaxAgeMissing) {
                                    Swal.fire({
                                        icon: "error",
                                        title: "Invalid Price and Age",
                                        text: "Please enter a price for children and private booking & age for children.",
                                    });
                                    priceFilled = false;
                                } else if (isAnyPriceMissing && !isAnyAgeMissing) {
                                    let missingPrice = [];
                                    if (isPrivatePriceMissing) missingPrice.push("private booking price");
                                    if (isChildPriceMissing) missingPrice.push("children price");

                                    Swal.fire({
                                        icon: "error",
                                        title: "Invalid Price",
                                        text: `Please enter ${missingPrice.join(" and ")}.`,
                                    });
                                    priceFilled = false;
                                } else if (!isAnyPriceMissing && isAnyAgeMissing) {
                                    let missingAge = [];
                                    if (isMinAgeMissing) missingAge.push("minimum age");
                                    if (isMaxAgeMissing) missingAge.push("maximum age");

                                    Swal.fire({
                                        icon: "error",
                                        title: "Invalid Age",
                                        text: `Please enter ${missingAge.join(" and ")} for children.`,
                                    });
                                    priceFilled = false;
                                } else if (isAnyPriceMissing && isAnyAgeMissing) {
                                    let missingPrice = [];
                                    if (isPrivatePriceMissing) missingPrice.push("private booking price");
                                    if (isChildPriceMissing) missingPrice.push("children price");

                                    let missingAge = [];
                                    if (isMinAgeMissing) missingAge.push("minimum age");
                                    if (isMaxAgeMissing) missingAge.push("maximum age");

                                    Swal.fire({
                                        icon: "error",
                                        title: "Invalid Price and Age",
                                        text: `Please enter ${missingPrice.join(" and ")} & ${missingAge.join(" and ")} for children.`,
                                    });
                                    priceFilled = false;
                                }
                                
                            }else{
                                priceFilled = true;
                            }
                        }
                    }
                // });



                
                if (current_step && priceFilled) {
                    Swal.fire({
                        icon: 'info',
                        title: @json(translate('stepper.please_wait')),
                        html: @json(translate('stepper.wait_message')),
                        timer: 2500,
                        allowOutsideClick: false,
                        timerProgressBar: true,
                        didOpen: () => {
                            Swal.showLoading()
                        },
                    }).then((result) => {
                        $.ajax({
                            url: `{{ route('create_update', ["category_ids" => $category->ids]) }}/${listing_id}`,
                            type: "POST",
                            headers: {
                                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                            },
                            data: form_data,
                            success: function(response) {
                                if (response.status) {
                                    // Set the listing_id if it was created/updated and not already set
                                    if (response.data && response.data.ids && !$('input[name="listing_id"]').val()) {
                                        $('input[name="listing_id"]').val(response.data.ids);
                                        console.log("Listing ID set after save and exit: ", response.data.ids);
                                    }

                                    Swal.fire({
                                        title: response.message ==
                                            @json(translate('stepper.listing_under_review')) ?
                                            @json(translate('stepper.well_done')) : @json(translate('stepper.success')),
                                        text: response.message,
                                        icon: "success"
                                    }).then((result) => {
                                        if (result.isConfirmed) {
                                            window.location.href =
                                                "{{ route('listing.index') }}";
                                        }
                                    });
                                } else {
                                    Swal.fire({
                                        title: @json(translate('stepper.error')),
                                        text: response.message,
                                        icon: "error"
                                    });
                                }
                                console.log("response", response);
                            },
                            error: function(xhr) {
                                alert(@json(translate('stepper.error')));
                                console.log("Error", xhr.responseText);
                            },
                            complete: function() {
                                // $button.html("Next");
                                // $button.prop("disabled", false); // Reset only the clicked button
                            }
                        });
                    })
                }
            })
        });
    </script>
@endpush
