<?php

namespace App\Services;

use App\Category;
use App\Listing;
use App\ListingAmenity;
use App\ListingDetail;
use App\ListingExperience;
use App\listingGallery;
use App\Mail\ListingPauseMail;
use App\Models\ListingAddress;
use App\Models\ListingAttribute;
use App\Models\ListingDiscount;
use App\Models\ListingFeatureItinerary;
use App\Models\ListingHourlyAvailability;
use App\Models\ListingRestrictedDay;
use App\Models\ListingRule;
use App\Models\ListingSeasonPrice;
use App\Models\ListingTourDuration;
use App\Models\ListingTourLanguage;
use Illuminate\Support\Str;
use App\Models\Wishlist;
use App\Traits\ListingTrait;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;

class ListingService
{
    use ListingTrait;
    function __construct(protected ?BookingService $bookingService = null) {}

    public  function storeImage($folderName, $file)
    {
        try {
            return Storage::disk('website')->put($folderName, $file);
        } catch (\Exception $e) {
            return '';
        } //end trycatch.
    } //end storeImage function.

    /**
     * Copy image file physically in the filesystem
     */
    private function copyImageFile($originalPath, $newFolder = null)
    {
        try {
            if (!Storage::disk('website')->exists($originalPath)) {
                return null;
            }

            // Get file info
            $pathInfo = pathinfo($originalPath);
            $extension = $pathInfo['extension'] ?? '';
            $originalFolder = dirname($originalPath);

            // Use original folder if no new folder specified
            $targetFolder = $newFolder ?? $originalFolder;

            // Generate new unique filename
            $newFilename = uniqid() . '.' . $extension;
            $newPath = $targetFolder . '/' . $newFilename;

            // Copy the file
            $fileContent = Storage::disk('website')->get($originalPath);
            Storage::disk('website')->put($newPath, $fileContent);

            return $newPath;
        } catch (\Exception $e) {
            Log::error('Error copying image file: ' . $e->getMessage());
            return null;
        }
    }
    function my_listing($keyword, $filter = 0, $perPage = 10)
    {
        try {
            // Build the query
            $query = Listing::query();

            // Apply user role filtering
            if (auth()->user()->hasRole(["user", "sub_admin"])) {
                $query->withSum('bookings', 'total_amount');
            } else {
                $query->with("bookings", "category", "files")->where("user_id", auth()->id())->withSum('bookings', 'total_amount');
            }

            // Apply search filter
            if (!empty($keyword)) {
                $query->where(function ($q) use ($keyword) {
                    $q->where('name', 'LIKE', "%$keyword%")
                        // ->orWhere('booking_capacity', 'LIKE', "%$keyword%")
                        // ->orWhere('free_cancelation_before', 'LIKE', "%$keyword%")
                        ->orWhere('description', 'LIKE', "%$keyword%");
                });
            }

            // Apply status filter
            if ($filter != '0') {
                switch ($filter) {
                    case '1': // Published
                        $query->where('status', 1);
                        break;
                    case '2': // Pending
                        $query->where('status', 0);
                        break;
                    case '3': // Accepted
                        $query->where('status', 1);
                        break;
                    case '4': // In Review
                        // $query->where('status', 2);
                        $query->where('status', 0);
                        break;
                    case '5': // Drafts
                        $query->where('status', 6);
                        break;
                }
            }

            $listing = $query->orderBy("id", "DESC")->paginate($perPage);
            return $listing;
        } catch (\Exception $e) {
            throw $e;
        }
    }
    // search listing by name
    function getListingsByName($listing_name)
    {
        try {
            $listings = Listing::query()
                ->active()
                // ->where('status', '1') // Assuming `active()` is a scope for active listings
                ->where(function ($query) use ($listing_name) {
                    $query->where('name', 'LIKE', "%{$listing_name}%")
                        ->orWhereHas('user', function ($userQuery) use ($listing_name) {
                            $userQuery->where('name', 'LIKE', "%{$listing_name}%");
                        });
                })
                ->with([
                    // 'detail:listing_id,basis_type,per_hour,per_day,child_price,adult_price',
                    // 'user:id,name,avatar',
                    // 'gallery_images',
                    // 'wishlist'
                ])
                ->orderBy('id', 'DESC')
                ->take(4)
                ->get();

            return $listings;
        } catch (\Exception $e) {
            // Log the exception for debugging (optional but recommended)
            throw $e; // Re-throw the exception for upstream handling
        }
    }
    public function getListings($category_id = null, $total_record = 10)
    {
        try {
            // Start the query with common relationships
            $query = Listing::with("detail:listing_id,basis_type,per_hour,per_day,child_price,adult_price", "user:id,name,avatar", "gallery_images", "wishlist", "seasons", "active_bookings")
                ->active()
                ->orderby("id", "DESC");

            // If a category ID is provided, filter by category
            if ($category_id) {
                $category = Category::find($category_id);
                if ($category) {
                    $query->where("category_id", $category_id);
                } else {
                    return null;
                }
            }

            // Return paginated results
            $listings = $query->select(["id", "ids", "user_id", "category_id", "name", "slug", "description", "lat", "created_at", "status"])
                ->paginate($total_record);
            return $listings;
        } catch (\Exception $e) {
            throw $e;
        }
    }
    function sortingImage($image_ids, $listing_id)
    {
        try {
            $listing = Listing::where("ids", $listing_id)->first();
            if (!$image_ids || !$listing_id) {
                return api_response(false, "Invalid data");
            }

            foreach ($image_ids as $index => $id) {
                $listing->gallery_images()
                    ->where('id', $id)
                    ->update(['sort_order' => $index + 1]);
            }
            return api_response(true, "Sort order updated successfully");
        } catch (\Exception $e) {
            return api_response(false, $e->getMessage());
        }
    }
    function url_code($listing_id)
    {
        try {
            $listing = Listing::where("ids", $listing_id)->first();
            if (!$listing->url_code) {
                $url_code = \Illuminate\Support\Str::random(8);
                $listing->url_code = $url_code;
                $listing->save();
            }
            return api_response(true, "Listing Url Created", route("redirectToListing", ["url_code" => $listing->url_code]));
        } catch (\Exception $e) {
            return api_response(false, $e->getMessage());
        }
    }
    public function duplicate($id)
    {
        DB::beginTransaction();
        try {
            $category_id = Listing::where("ids", $id)->first()->category_id;
            $listing = Listing::with($this->listing_relation($category_id))->where("ids", $id)->first();
            
            $newListing = $listing->replicate();
            $newListing->ids = Str::uuid();
            $newListing->name = $listing->name . ' (Copy)';
            $newListing->status = 6;
            $newListing->push();

            foreach ($this->listing_relation($listing->category_id) as $relation) {
                $relation = explode(':', $relation)[0]; // remove column filter if any
                // ✅ skip category relation
                if ($relation === 'category') continue;
                if ($relation === 'amenity_detail') continue;
                if ($relation === 'gallery_images') continue;
                if ($relation === 'files') continue;
                if ($relation === 'tour_languages') continue;
                if (! $listing->$relation) continue;

                if ($listing->$relation instanceof \Illuminate\Database\Eloquent\Collection) {
                    foreach ($listing->$relation as $item) {
                        $new = $item->replicate();
                        $new->listing_id = $newListing->id;
                        $new->save();
                    }
                } else {
                    $new = $listing->$relation->replicate();
                    $new->listing_id = $newListing->id;
                    $new->save();
                }
            }

            $newListing = $newListing->load($this->listing_relation($listing->category_id));
            $duplicate_listing = $newListing;
            $listing_gallery = listingGallery::where("listing_id", $listing->id)->get();
            foreach ($listing_gallery as $original_gallery) {
                $new_image_path = $this->copyImageFile($original_gallery->url);
                if ($new_image_path) {
                    $new_gallery = new listingGallery();
                    $new_gallery->listing_id = $duplicate_listing->id;
                    $new_gallery->name = $original_gallery->name;
                    $new_gallery->type = $original_gallery->type;
                    $new_gallery->url = $new_image_path;
                    $new_gallery->is_cover = $original_gallery->is_cover;
                    $new_gallery->sort_order = $original_gallery->sort_order;
                    $new_gallery->save();
                }
            }
            DB::commit();
            return $newListing;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    function coverImage($image_id, $listing_id)
    {
        try {
            $listing = Listing::where("ids", $listing_id)->first();
            if (!$listing) {
                return api_response(false, "Listing not found");
            }
            $listing_gallery = listingGallery::where("id", $image_id)
                ->where("listing_id", $listing->id)
                ->first();
            if (!$listing_gallery) {
                return api_response(false, "Image not found");
            }
            $cover_image = listingGallery::where("is_cover", 1)
                ->where("listing_id", $listing->id)
                ->first();
            if ($cover_image) {
                $temp_sort_order = $cover_image->sort_order;
                $cover_image->is_cover = 0;
                $cover_image->sort_order = $listing_gallery->sort_order;
                $cover_image->save();

                $listing_gallery->is_cover = 1;
                $listing_gallery->sort_order = $temp_sort_order;
            } else {
                $listing_gallery->is_cover = 1;
            }
            $listing_gallery->save();
            return api_response(true, "Cover image updated successfully");
        } catch (\Exception $e) {
            return api_response(false, $e->getMessage());
        }
    }
    function listing_relation($category_id)
    {
        $relationships = [
            1 => [
                "category",
                "detail:listing_id,booking_capacity,tour_day_type,start_time,end_time,child_allow,child_age_from,child_age_to,child_price,adult_price,private_booking,private_booking_price,minimum_stay_length,listing_availability_type,listing_availability_period,advance_booking_period,cancellation_policy,pet,booking_close_time",
                "address",
                "tour_durations",
                "tour_languages_pivot",
                "tour_languages",
                "amenity_detail",
                "amenities",
                "itineraries",
                "key_features",
                "includes",
                "not_includes",
                "rules",
                "notes",
                "gallery_images",
                "files",
                "discount",
                "seasons",
            ],
            2 => [
                "category",
                "detail:listing_id,basis_type,per_hour,per_day,capacity,chauffeur,boat_captain,minimum_stay_length,boat_length,listing_availability_type,listing_availability_period,advance_booking_period,cancellation_policy,price_change,pet,check_in_time,check_out_time,booking_close_time",
                "address",
                "amenities",
                "amenity_detail",
                "key_features",
                "includes",
                "not_includes",
                "rules",
                "notes",
                "hourly_availabilities",
                "gallery_images",
                "files",
                "seasons",
                "discount",
            ],
            3 => [
                "category",
                "detail:listing_id,basis_type,per_hour,per_day,seats,transmission,engine_type,minimum_stay_length,listing_availability_type,listing_availability_period,advance_booking_period,cancellation_policy,price_change,pet,booking_close_time,placa_restriction",
                "address",
                "restricted_days",
                "key_features",
                "rules",
                "notes",
                "gallery_images",
                "files",
                "includes",
                "not_includes",
                "hourly_availabilities",
                "discount",
                "seasons",
            ],
            4 => [
                "category",
                "detail:listing_id,basis_type,per_day,guests,bedrooms,bathrooms,minimum_stay_length,listing_availability_type,listing_availability_period,advance_booking_period,cancellation_policy,pet,check_in_time,check_out_time,booking_close_time",
                "address",
                "amenities",
                "amenity_detail",
                "key_features",
                "rules",
                "notes",
                "gallery_images",
                "files",
                "discount",
                "seasons",
            ],
        ];
        return $relationships[$category_id] ?? [];
    }
    function detail($slug, $listing_id)
    {
        $category_id = Listing::where("slug", $slug)
            ->where("ids", $listing_id)
            ->where("pause", 0)
            ->where("status", 1)
            ->value("category_id");

        if (!$category_id) {
            return api_response(false, "Listing data not found", null);
        }

        $base = ["wishlist", "reviews", "reservation_dates:id,listing_id,date"];
        $relationships = $this->listing_relation($category_id);
        $with = array_merge($relationships, $base);

        $listing = Listing::with($with)
            ->where("slug", $slug)
            ->where("ids", $listing_id)
            ->where("pause", 0)
            ->where("status", 1)
            ->first();

        if (!$listing) {
            return api_response(false, "Listing data not found", null);
        }

        $listing->increment('click_through_rate');

        return api_response(true, "Listing data found", $listing);
    }


    /**
     * Get listing detail with availability and booking dates
     * This method handles the complex logic for calculating available and reserved dates
     */
    function getListingDetailWithAvailability($slug, $listing_id)
    {
        try {
            // Get the basic listing detail first
            $listing_service = $this->detail($slug, $listing_id);

            if ($listing_service["status"] != true) {
                return api_response(false, "Listing not found", null);
            }

            $listing = $listing_service["data"];
            $listing_detail = $listing->detail;

            // Get today's date for filtering using Columbia timezone
            $today = now('America/Bogota')->format('Y-m-d');

            // Get reservation dates (blocked dates set by owner)
            // Try to use 'date' column first, fallback to 'start' column if 'date' doesn't exist
            // $reserve_dates = \App\Models\ReservationDate::where("listing_id", $listing->id)->pluck('date')->toArray();
            $reserve_dates = $listing->reservation_dates->map(fn($d) => $d->date ?? $d->start)
                ->filter()
                ->unique()
                ->values()
                ->all();

            // Filter out past dates and remove any null values
            $reserve_dates_array = array_filter($reserve_dates, function ($date) use ($today) {
                return $date && $date >= $today;
            });

            // Add current date if booking close time has passed
            if ($listing->detail->booking_close_time) {
                $now = now('America/Bogota');
                $bookingCloseTime = \Carbon\Carbon::createFromFormat('h:i A', $listing->detail->booking_close_time, 'America/Bogota');
                if ($now->greaterThan($bookingCloseTime)) {
                    $reserve_dates_array[] = now('America/Bogota')->format('Y-m-d');
                }
            }

            // Calculate availability period based on listing settings
            $enable_dates = [];
            $availability_period = $listing_detail->listing_availability_period ?? 3; // Default to 3 months

            // Generate available dates from today to the availability period
            $start_date = now('America/Bogota');
            $end_date = now('America/Bogota')->addMonths($availability_period);

            for ($date = $start_date->copy(); $date->lte($end_date); $date->addDay()) {
                $enable_dates[] = $date->format('Y-m-d');
            }

            // Calculate booking dates (dates that are already booked)
            $current_booking_dates = $this->calculateBookingDates($listing, $listing_detail);

            // Filter out past dates from booking dates as well
            $current_booking_dates = array_filter($current_booking_dates, function ($date) use ($today) {
                return $date >= $today;
            });

            // Merge all unavailable dates
            $reserve_dates_array = array_merge($reserve_dates_array, $current_booking_dates);

            // Remove unavailable dates from enable_dates to get final available dates
            $enable_dates = array_values(array_diff($enable_dates, $reserve_dates_array));

            return api_response(true, "Listing detail with availability found", [
                'listing' => $listing,
                'category' => $listing->category,
                'reviews' => $listing->reviews()->with('user:id,name,avatar')->get(),
                'reserve_dates_array' => $reserve_dates_array,
                'enable_dates' => $enable_dates
            ]);
        } catch (\Exception $e) {
            return api_response(false, $e->getMessage(), null);
        }
    }

    /**
     * Calculate booking dates based on listing type and existing bookings
     */
    private function calculateBookingDates($listing, $listing_detail)
    {
        $current_booking_dates = [];

        // Handle hourly bookings
        if ($listing->detail->basis_type == "Hourly") {
            $bookedDates = $listing->booked_slots->groupBy('date');
            $hourlyAvailability = $listing->hourly_availabilities->pluck('full_time')->toArray();

            foreach ($bookedDates as $date => $bookings) {
                $bookedSlots = $bookings->pluck('slot')->toArray();
                if (count(array_intersect($hourlyAvailability, $bookedSlots)) === count($hourlyAvailability)) {
                    $current_booking_dates[] = $date;
                }
            }
        }

        // Handle tour bookings
        if ($listing->detail->basis_type == "Tour") {
            $requiredCapacity = $listing->detail->booking_capacity;
            $bookings = $listing->bookings()->where("tour_type", "private")->get(['check_in', 'check_out']);
            $guest_bookings = $listing->bookings()->get();

            $uniqueDatesWithGuests = $guest_bookings->groupBy(function ($guest_booking) {
                return \Carbon\Carbon::parse($guest_booking->check_in)->format('Y-m-d');
            });

            foreach ($uniqueDatesWithGuests as $date => $group) {
                $guestSum = $group->sum('guest');
                if ($guestSum >= $requiredCapacity) {
                    $current_booking_dates[] = $date;
                }
            }
        } else {
            $bookings = $listing->bookings()->where("listing_basis", "Daily")->orWhere("tour_type", "private")->get(['check_in', 'check_out']);
        }

        // Process booking date ranges
        if (isset($bookings)) {
            foreach ($bookings as $booking) {
                $startDate = \Carbon\Carbon::parse($booking->check_in);
                if ($booking->check_in != $booking->check_out) {
                    $endDate = \Carbon\Carbon::parse($booking->check_out)->subDay();
                } else {
                    $endDate = \Carbon\Carbon::parse($booking->check_out);
                }

                $advanceDays = is_numeric($listing_detail->advance_booking_period) ? (int) $listing_detail->advance_booking_period : 0;
                if ($advanceDays > 0) {
                    $startDate = \Carbon\Carbon::parse($booking->check_in)->subDays($advanceDays);
                    $endDate = \Carbon\Carbon::parse($booking->check_out)->addDays($advanceDays);
                }

                for ($date = $startDate; $date->lte($endDate); $date->addDay()) {
                    $current_booking_dates[] = $date->format('Y-m-d');
                }
            }
        }

        return array_values(array_unique($current_booking_dates));
    }

    // Add Listing
    function add_listing($request_data, $category_id)
    {
        try {
            $request_data["user_id"] = auth()->id();
            if (isset($request_data["name"])) {
                $request_data["slug"] = str_slug($request_data["name"]);
            }
            $request_data["status"] = $request_data["listing_status"];
            $request_data["ids"] = Str::uuid();
            $listing = Listing::create($request_data);
            if ($listing) {
                $request_data["listing_id"] = $listing->id;
                if (array_key_exists("listing_availability_period", $request_data)) {
                    if ($request_data["listing_availability_period"] == "day") {
                        $request_data["listing_availability_period"] = Carbon::now('America/Bogota')->addDay();
                        $request_data["listing_availability_type"] = "day";
                    } elseif ($request_data["listing_availability_period"] == "week") {
                        $request_data["listing_availability_period"] = Carbon::now('America/Bogota')->addWeek();
                        $request_data["listing_availability_type"] = "week";
                    } elseif ($request_data["listing_availability_period"] == "month") {
                        $request_data["listing_availability_period"] = Carbon::now('America/Bogota')->addMonth();
                        $request_data["listing_availability_type"] = "month";
                    } elseif ($request_data["listing_availability_period"] == "2 month") {
                        $request_data["listing_availability_period"] = Carbon::now('America/Bogota')->addMonth(2);
                        $request_data["listing_availability_type"] = "2 month";
                    }
                }
                if ($category_id == 4) {
                    $request_data["basis_type"] = "Daily";
                } else if ($category_id == 1) {
                    $request_data["basis_type"] = "Tour";
                }
                if (in_array($category_id, [2, 3, 4])) {
                    if (array_key_exists("basis_type", $request_data)) {
                        if ($request_data["basis_type"] == "Daily") {
                            $request_data["per_day"] = $request_data["price"];
                        } else if ($request_data["basis_type"] == "Hourly") {
                            $request_data["per_hour"] = $request_data["price"];
                        }
                    }
                }
                if (($request_data["tour_day_type"] ?? "") == "multiple-days" && $category_id == 1) {
                    $request_data["start_time"] = null;
                    $request_data["end_time"] = null;
                }
                if (!empty($request_data['seasonal_price_chk']) && $request_data['seasonal_price_chk'] === 'on') {
                    $request_data['price_change'] = $request_data['price_change'] ?? null;
                    $request_data['season_start'] = $request_data['season_start'] ?? null;
                    $request_data['season_end'] = $request_data['season_end'] ?? null;
                } else {
                    $request_data['price_change'] = null;
                    $request_data['season_start'] = null;
                    $request_data['season_end'] = null;
                }
                ListingDetail::create($request_data);
                $listing_attribute = $this->listing_attribute($category_id, $request_data, $listing->id);
                if ($listing_attribute) {
                    return api_response(true, "Data added", $listing);
                } else {
                    return api_response(false, "Something went wrong in attribute", null);
                }
            }
            return $listing;
        } catch (\Throwable $th) {
            return api_response(false, $th->getMessage(), null);
        }
    }

    // update Listing 
    function update_listing($request_data, $listing_id, $category_id)
    {
        try {
            // $request_data["user_id"] = auth()->id();
            if (isset($request_data["name"])) {
                $request_data["slug"] = str_slug($request_data["name"]);
            }
            $category = Category::find($category_id);
            if (!$category) {
                return api_response(false, "Category not found", null);
            }
            $listing = Listing::where("category_id", $category_id)->find($listing_id);
            if ($listing) {
                $request_data["listing_id"] = $listing->id;
                if ($listing->status == 1) {
                    $request_data["status"] = 1;
                } elseif ($listing->status == 0) {
                    $request_data["status"] = 0;
                } elseif ($listing->status == 6 && $request_data["listing_status"] == 0) {
                    $request_data["status"] = 0;
                } else {
                    $request_data["status"] = 6;
                }
                // $request_data["status"] = $request_data["listing_status"];
                $listing->update($request_data);
                $listing_detail = ListingDetail::where("listing_id", $listing->id)->first();
                if ($listing_detail) {
                    if (array_key_exists("listing_availability_period", $request_data)) {
                        if ($request_data["listing_availability_period"] == "day") {
                            $request_data["listing_availability_period"] = Carbon::now('America/Bogota')->addDay();
                            $request_data["listing_availability_type"] = "day";
                        } elseif ($request_data["listing_availability_period"] == "week") {
                            $request_data["listing_availability_period"] = Carbon::now('America/Bogota')->addWeek();
                            $request_data["listing_availability_type"] = "week";
                        } elseif ($request_data["listing_availability_period"] == "month") {
                            $request_data["listing_availability_period"] = Carbon::now('America/Bogota')->addMonth();
                            $request_data["listing_availability_type"] = "month";
                        } elseif ($request_data["listing_availability_period"] == "2 month") {
                            $request_data["listing_availability_period"] = Carbon::now('America/Bogota')->addMonth(2);
                            $request_data["listing_availability_type"] = "2 month";
                        }
                    }
                    if ($category_id == 4) {
                        $request_data["basis_type"] = "Daily";
                    } else if ($category_id == 1) {
                        $request_data["basis_type"] = "Tour";
                    }
                    if (in_array($category_id, [2, 3, 4])) {
                        if (array_key_exists("basis_type", $request_data)) {
                            if ($request_data["basis_type"] == "Daily") {
                                $request_data["per_day"] = $request_data["price"];
                            } else if ($request_data["basis_type"] == "Hourly") {
                                $request_data["per_hour"] = $request_data["price"];
                            }
                        }
                    }
                    if (($request_data["tour_day_type"] ?? "") == "multiple-days" && $category_id == 1) {
                        $request_data["start_time"] = null;
                        $request_data["end_time"] = null;
                    }
                    if (!empty($request_data['seasonal_price_chk']) && $request_data['seasonal_price_chk'] === 'on') {
                        $request_data['price_change'] = $request_data['price_change'] ?? null;
                        $request_data['season_start'] = $request_data['season_start'] ?? null;
                        $request_data['season_end'] = $request_data['season_end'] ?? null;
                        $request_data['season_type'] = $request_data['season_type'] ?? null;
                    } else {
                        $request_data['price_change'] = null;
                        $request_data['season_start'] = null;
                        $request_data['season_end'] = null;
                        $request_data['season_type'] = null;
                    }

                    $listing_detail->update($request_data);
                }

                $this->listing_attribute($category->id, $request_data, $listing->id);
            } else {
                return api_response(false, "listing not found", null);
            }
            return api_response(true, "listing Updated", $listing);
        } catch (\Exception $e) {
            return api_response(false, $e->getMessage(), null);
        }
    }
    function create_update($request_data, $category_id)
    {
        DB::beginTransaction();
        try {
            if (auth()->user()->hasRole(["user", "sub_admin"])) {
                $listing = Listing::where("ids", $request_data["listing_id"])->where("category_id", $category_id)->first();
            } else {
                $listing = Listing::where("ids", $request_data["listing_id"])->where("user_id", auth()->id())->where("category_id", $category_id)->first();
            }
            $request_data["step_no"] = $request_data["current_step"];
            if (isset($request_data["name"])) {
                $request_data["slug"] = str_slug($request_data["name"]);
            }
            if ($listing?->status == 1) {
                $request_data["status"] = 1;
            } elseif ($listing?->status == 0 && ($request_data["listing_status"] ?? null) == 0) {
                // If the listing is pending, it cannot go back to drafting
                $request_data["status"] = 0;
            } elseif ($listing?->status == 6) {
                // If the listing is drafting
                if ($request_data["listing_status"] == 6) {
                    $request_data["status"] = 6; // Stay as draft
                } elseif ($request_data["listing_status"] == 0) {
                    $request_data["status"] = 0; // Move to pending
                } elseif ($request_data["listing_status"] == 1) {
                    $request_data["status"] = 1; // Move to active
                }
            }
            if (!$listing) {
                $request_data["user_id"] = auth()->id();
                $listing = Listing::create($request_data);
            } else {
                $request_data["user_id"] = $listing->user_id;
                $listing->update($request_data);
            }
            $request_data["listing_id"] = $listing->id;
            $listing_address = ListingAddress::where("listing_id", $listing->id)->first();
            if (!$listing_address) {
                $listing_address = ListingAddress::create($request_data);
            } else {
                $listing_address->update($request_data);
            }
            $listing_detail = $this->listing_detail($request_data, $listing);
            if ($listing_detail["status"] == false) {
                return api_response(false, "Something went wrong in detail", null);
            }
            $listing_attribute = $this->listing_attribute($category_id, $request_data, $listing->id);
            if (($listing_attribute["status"] ?? false) == false) {
                return api_response(false, "Something went wrong in attribute", null);
            }
            DB::commit();
            if ($listing->status == 6) {
                $message = translate("stepper.listing_saved_in_draft");
            } elseif ($listing->status == 0) {
                $message = translate("stepper.listing_under_review");
            } else {
                $message = translate("stepper.your_listing_is_now_live_and_visible_to_customers");
            }
            return api_response(true, $message, $listing);
        } catch (\Exception $e) {
            DB::rollback();
            // throw $e;
            return api_response(false, "An error occurred: " . $e->getMessage());
        }
    }
    function duplicate_listing($request_data, $category_id)
    {
        DB::beginTransaction();
        try {
            $request_data["listing_status"] = 0; // Set as draft

            // Get the original listing ID - could be from listing_id or original_listing_id
            $original_listing_id = $request_data["original_listing_id"] ?? $request_data["listing_id"] ?? null;

            if (!$original_listing_id) {
                throw new \Exception("Original listing ID not provided");
            }

            // Ensure the name has (Copy) suffix if not already present
            if (!str_contains($request_data["name"], "(Copy)")) {
                $request_data["name"] = $request_data["name"] . " (Copy)";
            }

            // Remove listing_id and original_listing_id from request data to create new listing
            $request_data["listing_id"] = $request_data["listing_id"] ?? null;
            unset($request_data["original_listing_id"]);
            // unset($request_data["is_duplication"]);

            $listing_create = $this->create_update($request_data, $category_id);
            if ($listing_create["status"] == false) {
                return api_response(false, "Something went wrong in creating duplicate." . " " . $listing_create["message"], null);
            }


            // duplicate images of gallery
            if (isset($listing_create["data"])) {
                $duplicate_listing = $listing_create["data"];

                $listing_gallery = listingGallery::where("listing_id", $original_listing_id)->get();
                foreach ($listing_gallery as $original_gallery) {
                    // Copy the physical image file
                    $new_image_path = $this->copyImageFile($original_gallery->url);

                    if ($new_image_path) {
                        // Create new gallery entry for the duplicated listing
                        $new_gallery = new listingGallery();
                        $new_gallery->listing_id = $duplicate_listing->id;
                        $new_gallery->name = $original_gallery->name;
                        $new_gallery->type = $original_gallery->type;
                        $new_gallery->url = $new_image_path;
                        $new_gallery->is_cover = $original_gallery->is_cover;
                        $new_gallery->sort_order = $original_gallery->sort_order;
                        $new_gallery->save();
                    }
                }
            }
            DB::commit();

            // return api_response(true, "Listing Duplicated Successfully", $listing_create["data"] ?? null);

            $listing = $listing_create["data"];
            if ($listing->status == 6) {
                $message = translate("stepper.listing_saved_in_draft");
            } elseif ($listing->status == 0) {
                $message = translate("stepper.listing_under_review");
            } else {
                $message = translate("stepper.your_listing_is_now_live_and_visible_to_customers");
            }

            return api_response(true, $message, $listing ?? null);
        } catch (\Exception $e) {
            DB::rollback();
            return api_response(false, "An error occurred: " . $e->getMessage());
        }
    }
    function listing_show($listing_id)
    {
        try {
            $category_id = Listing::where("ids", $listing_id)
                ->where("user_id", auth()->id())
                ->value("category_id");

            if (!$category_id) {
                throw new \Exception("Listing not found");
            }
            $listingRelation = $this->listing_relation($category_id);
            $listing_detail = Listing::with($listingRelation)->where("ids", $listing_id)->first();
            return $listing_detail;
        } catch (\Exception $e) {
            throw $e;
        }
    }

    function filter_listing($request_data)
    {
        $searchArray = $request_data;
        // parse_str($request_data['search_data'], $searchArray);
        // $searchArray = $request_data["search_data"];
        $query = Listing::query()->with('detail');
        // Combine listing name and provider name filters into one
        if (!empty($searchArray['listing_name'])) {
            $listingName = $searchArray['listing_name'];
            $query->where(function ($query) use ($listingName) {
                $query->where('name', 'LIKE', "%{$listingName}%")
                    ->orWhereHas('user', function ($q) use ($listingName) {
                        $q->where('name', 'LIKE', "%{$listingName}%");
                    });
            });
        }
        // Category ID filter
        if (!empty($searchArray['category_id'])) {
            $query->where('category_id', $searchArray['category_id']);
        }
        // Availability period filter
        if (!empty($searchArray['startDate']) && !empty($searchArray['endDate'])) {
            $today = Carbon::now('America/Bogota')->format('Y-m-d'); // Get today's date in YYYY-MM-DD format using Columbia timezone
            $startDate = $searchArray['startDate'];
            $endDate = $searchArray['endDate'];
            $query->whereHas('detail', function ($subQuery) use ($startDate, $endDate, $today) {
                $subQuery->where(function ($q) use ($startDate, $endDate, $today) {
                    $q->where(function ($sub) use ($startDate, $today) {
                        $sub->whereDate(DB::raw("'$startDate'"), '>=', $today)
                            ->whereDate(DB::raw("'$startDate'"), '<=', DB::raw("DATE_ADD(CURDATE(), INTERVAL listing_availability_period MONTH)"));
                    })->orWhere(function ($sub) use ($endDate, $today) {
                        $sub->whereDate(DB::raw("'$endDate'"), '>=', $today)
                            ->whereDate(DB::raw("'$endDate'"), '<=', DB::raw("DATE_ADD(CURDATE(), INTERVAL listing_availability_period MONTH)"));
                    });
                });
            });
            // $query->whereDoesntHave('searchbookings', function ($subQuery) use ($startDate, $endDate) {
            //     // Exclude bookings with status 0, 1, 3 and where check-in or check-out overlap with the date range
            //     $subQuery->whereNotIn('status', [3, 7])
            //         ->where(function ($query) use ($startDate, $endDate) {

            //             $query->whereBetween('check_in', [$startDate, $endDate])
            //                 ->orWhereBetween('check_out', [$startDate, $endDate]);
            //         });
            // });
        }
        // Location filter (latitude, longitude)
        // if (!empty($searchArray['lat']) && !empty($searchArray['lng'])) {
        //     $lat = $searchArray['lat'];
        //     $lng = $searchArray['lng'];
        //     $distanceLimit = 50; // Adjust to include listings within 50 km
        //     $radiusConstant = 6371; // Earth radius in kilometers

        //     $query->select("*")
        //         ->selectRaw(
        //             "
        //             id, 
        //             ($radiusConstant * acos(cos(radians(?)) * cos(radians(lat)) * cos(radians(lng) - radians(?)) + sin(radians(?)) * sin(radians(lat)))) AS distance",
        //             [$lat, $lng, $lat] // Binding the correct parameters
        //         )
        //         ->havingRaw("distance <= ?", [$distanceLimit]) // Include listings within 50 km
        //         ->orderBy('distance', 'asc'); // Sort by proximity
        // }
        // Location filter (latitude, longitude)
        // if (!empty($searchArray['lat']) && !empty($searchArray['lng'])) {
        //     $lat = $searchArray['lat'];
        //     $lng = $searchArray['lng'];
        //     $distanceLimit = 100; // Limit to 50 km
        //     $apiKey = google_map_key(); // Replace with your actual API key
        //     // Fetch all listings with coordinates
        //     $listings = $query->get(); // Fetch all listings initially

        //     $filteredListings = [];
        //     $destinations = implode('|', $listings->map(function ($listing) {
        //         return "{$listing->lat},{$listing->lng}";
        //     })->toArray());
        //     // Call Google Distance Matrix API
        //     $url = "https://maps.googleapis.com/maps/api/distancematrix/json?origins={$lat},{$lng}&destinations={$destinations}&key={$apiKey}";
        //     $response = Http::get($url);
        //     if ($response->successful()) {
        //         $data = $response->json();
        //         if (isset($data['rows'][0]['elements'])) {
        //             foreach ($listings as $index => $listing) {
        //                 $distanceData = $data['rows'][0]['elements'][$index];
        //                 if (isset($distanceData['distance']['value'])) {
        //                     $distanceInKm = $distanceData['distance']['value'] / 1000; // Convert meters to km
        //                     $durationInMinutes = $distanceData['duration']['value'] / 60; // Convert seconds to minutes
        //                     if ($distanceInKm <= $distanceLimit) {
        //                         // Add the distance as a property to the listing object
        //                         $listing->distance = $distanceInKm;
        //                         $listing->duration = $durationInMinutes;
        //                         // Optionally filter listings within a specific distance limit
        //                         if ($distanceInKm <= $distanceLimit) {
        //                             $filteredListings[] = $listing; // Add listing to filtered results
        //                         }
        //                     }
        //                 }
        //             }
        //         }
        //     }
        //     if (!empty($filteredListings)) {
        //         // Convert to collection and sort by distance
        //         // $query = collect($filteredListings)->sortBy('distance');
        //         // $query = collect([]);
        //         return $filteredListings;
        //         //return response()->json($filteredListings);
        //     } else {
        //         return $filteredListings;
        //         // return response()->json($filteredListings);
        //         //$query = collect([]); // Return an empty collection if no listings are within range
        //     }
        // }
        //latest radius code
        if (!empty($searchArray['lat']) && !empty($searchArray['lng'])) {
            $lat = $searchArray['lat'];
            $lng = $searchArray['lng'];
            $distanceLimit = 50; // Limit to 50 km
            $apiKey = google_map_key();
            $listings = $query->get(); // Keep query here to retain other filters
            $filteredListings = [];
            $destinations = implode('|', $listings->map(function ($listing) {
                return "{$listing->lat},{$listing->lng}";
            })->toArray());
            $url = "https://maps.googleapis.com/maps/api/distancematrix/json?origins={$lat},{$lng}&destinations={$destinations}&key={$apiKey}";
            $response = Http::get($url);
            if ($response->successful()) {
                $data = $response->json();
                if (isset($data['rows'][0]['elements'])) {
                    foreach ($listings as $index => $listing) {
                        $distanceData = $data['rows'][0]['elements'][$index];
                        if (isset($distanceData['distance']['value'])) {
                            $distanceInKm = $distanceData['distance']['value'] / 1000; // Convert meters to km
                            $durationInMinutes = $distanceData['duration']['value'] / 60; // Convert seconds to minutes
                            if ($distanceInKm <= $distanceLimit) {
                                $listing->distance = $distanceInKm;
                                $listing->duration = $durationInMinutes;
                                $filteredListings[] = $listing->id;
                            }
                        }
                    }
                }
            }
            // Filter the query further based on distance
            if (!empty($filteredListings)) {
                $query->whereIn('id', $filteredListings);
            } else {
                return collect(); // Return an empty collection if no listings match
            }
        }
        //latest radius code end
        // if (!empty($searchArray['lat']) && !empty($searchArray['lng'])) {
        //     $lat = $searchArray['lat'];
        //     $lng = $searchArray['lng'];
        //     $distanceLimit = 1; // Adjust as needed
        //     $radiusConstant = 3959; // Adjust based on miles or kilometers

        //     $query->select("*")
        //         ->selectRaw(
        //             "
        //     id, 
        //     ($radiusConstant * acos(cos(radians(?)) * cos(radians(lat)) * cos(radians(lng) - radians(?)) + sin(radians(?)) * sin(radians(lat)))) AS distance",
        //             [$lat, $lng, $lat] // Binding the correct number of parameters
        //         )
        //         ->havingRaw("distance < ?", [$distanceLimit])
        //         ->orderBy('distance', 'asc');
        // }
        // for accomodation category
        if (isset($searchArray['category_id']) && in_array($searchArray['category_id'], [4])) {
            if (isset($searchArray['price_range']) && !empty($searchArray['price_range'])) {
                $price_range =  explode(";", $searchArray['price_range']);
                $min_price = $price_range[0];
                $max_price = $price_range[1];
                $query->whereHas('detail', function ($subQuery) use ($min_price, $max_price) {
                    $subQuery->whereBetween('per_day', [$min_price, $max_price]);
                });
            }
            if (isset($searchArray['listing_types']) && !empty($searchArray["listing_types"])) {
                $listing_types = $searchArray["listing_types"];
                $query->whereIn('type_id', $listing_types);
            }
            if (isset($searchArray["ratings"]) && !empty($searchArray["ratings"])) {
                $rating = $searchArray["ratings"];
                $query->whereHas('reviews', function ($subQuery) use ($rating) {
                    $subQuery->where('rating', $rating);
                });
            }
            //for experiences bedrooms
            if (isset($searchArray['bedroom']) && !empty($searchArray['bedroom'])) {
                $bedroom = $searchArray['bedroom'];
                $query->whereHas('detail', function ($subQuery) use ($bedroom) {
                    $subQuery->where('bedrooms', $bedroom);
                });
            }
            if (isset($searchArray['bathroom']) && !empty($searchArray['bathroom'])) {
                $bathroom = $searchArray['bathroom'];
                $query->whereHas('detail', function ($subQuery) use ($bathroom) {
                    $subQuery->where('bathrooms', $bathroom);
                });
            }
            //for experiences amenities
            if (isset($searchArray['amenities']) && !empty($searchArray['amenities'])) {
                $selected_amenities = $searchArray['amenities'];
                $query->whereHas('amenities', function ($subQuery) use ($selected_amenities) {
                    $subQuery->whereIn('amenity_id', $selected_amenities);
                });
            }
            //for experiences pet
            if (isset($searchArray['pet']) && !empty($searchArray['pet'])) {
                $pet = $searchArray['pet'];
                $query->whereHas('detail', function ($subQuery) use ($pet) {
                    $subQuery->where('pet', $pet);
                });
            }
        }
        // accomodation category end

        // for tour expereiences
        if (isset($searchArray['category_id']) && in_array($searchArray['category_id'], [1])) {
            if (isset($searchArray['price_range']) && !empty($searchArray['price_range'])) {
                $price_range =  explode(";", $searchArray['price_range']);
                $min_price = $price_range[0];
                $max_price = $price_range[1];
            }
            if (isset($searchArray['price_range_type']) && $searchArray['price_range_type'] == 'child') {
                $query->whereHas('detail', function ($subQuery) use ($min_price, $max_price) {
                    $subQuery->whereBetween('child_price', [$min_price, $max_price]);
                });
            } elseif (isset($searchArray['price_range_type']) && $searchArray['price_range_type'] == 'adult') {
                $query->whereHas('detail', function ($subQuery) use ($min_price, $max_price) {
                    $subQuery->whereBetween('adult_price', [$min_price, $max_price]);
                });
            }
            if (isset($searchArray['tour_activity_length'])) {
                $tour_activity_length = $searchArray['tour_activity_length'];
                if ($tour_activity_length != 'any') {
                    $query->whereHas('tour_durations', function ($subQuery) use ($tour_activity_length) {
                        $subQuery->where(function ($query) use ($tour_activity_length) {
                            $query->whereColumn('end_time', '>=', 'start_time')
                                ->whereRaw('HOUR(TIMEDIFF(end_time, start_time)) >= ?', [$tour_activity_length]);
                        });
                    });
                }
            }
            if (isset($searchArray['tour_day_type']) && !empty($searchArray['tour_day_type'])) {
                $tour_day_type = $searchArray['tour_day_type'];
                $query->whereHas('detail', function ($subQuery) use ($tour_day_type) {
                    $subQuery->whereIn('tour_day_type', $tour_day_type);
                });
            }
            if (isset($searchArray['listing_types']) && !empty($searchArray["listing_types"])) {
                $listing_types = $searchArray["listing_types"];
                $query->whereIn('type_id', $listing_types);
            }
            if (isset($searchArray['private_booking']) && !empty($searchArray['private_booking'])) {
                $private_booking = $searchArray['private_booking'];
                $query->whereHas('detail', function ($subQuery) use ($tour_day_type) {
                    $subQuery->whereIn('tour_day_type', $tour_day_type);
                });
            }
            if (isset($searchArray['child_allow']) && !empty($searchArray['child_allow'])) {
                $child_allow = $searchArray['child_allow'];
                $query->whereHas('detail', function ($subQuery) use ($child_allow) {
                    $subQuery->where('child_allow', $child_allow);
                });
            }

            if (isset($searchArray['languages']) && !empty($searchArray['languages'])) {
                $tour_languages = $searchArray['languages'];
                $query->whereHas('tour_languages', function ($subQuery) use ($tour_languages) {
                    // Ensure we check for any of the selected language IDs
                    $subQuery->whereIn('tour_language_id', $tour_languages);
                });
            }

            if (isset($searchArray['amenities']) && !empty($searchArray['amenities'])) {
                $amenities = $searchArray['amenities'];
                $query->whereHas('amenity_detail', function ($subQuery) use ($amenities) {
                    // Ensure we check for any of the selected language IDs
                    $subQuery->whereIn('amenity_id', $amenities);
                });
            }

            if (isset($searchArray["ratings"]) && !empty($searchArray["ratings"])) {
                $rating = $searchArray["ratings"];
                $query->whereHas('reviews', function ($subQuery) use ($rating) {
                    $subQuery->where('rating', $rating);
                });
            }
            if ((isset($searchArray["adults"]) && !empty($searchArray["adults"])) || (isset($searchArray["children"]) && !empty($searchArray["children"]))) {
                $adults = $searchArray["adults"] ?? 0;
                $children = $searchArray["children"] ?? 0;
                $total_guests = $adults + $children;

                // Check if date variables exist and have values
                $hasValidDates = (isset($startDate) && isset($endDate) && !empty($startDate) && !empty($endDate));

                if ($hasValidDates) {
                    // If dates are provided, subtract overlapping bookings
                    $query->whereHas('detail', function ($subQuery) use ($total_guests, $startDate, $endDate) {
                        $subQuery->whereRaw('
                            booking_capacity >= ? + COALESCE((
                                SELECT SUM(bd.adult_number + bd.child_number)
                                FROM bookings b
                                JOIN booking_details bd ON b.id = bd.booking_id
                                WHERE b.listing_id = listings.id
                                AND b.status NOT IN (3, 7)
                                AND (
                                    (b.check_in BETWEEN ? AND ?) OR 
                                    (b.check_out BETWEEN ? AND ?) OR
                                    (b.check_in <= ? AND b.check_out >= ?) OR
                                    (? BETWEEN b.check_in AND b.check_out) OR
                                    (? BETWEEN b.check_in AND b.check_out)
                                )
                            ), 0)
                        ', [
                            $total_guests,
                            $startDate,
                            $endDate,
                            $startDate,
                            $endDate,
                            $startDate,
                            $endDate,
                            $startDate,
                            $endDate
                        ]);
                    });
                } else {
                    // If no dates provided, just check booking capacity >= total guests
                    $query->whereHas('detail', function ($subQuery) use ($total_guests) {
                        $subQuery->where('booking_capacity', '>=', $total_guests);
                    });
                }
            }
        }
        // tour experience end
        // for watercraft boat start
        if (isset($searchArray['category_id']) && in_array($searchArray['category_id'], [2])) {
            if (isset($searchArray['price_range']) && !empty($searchArray['price_range'])) {
                $price_range =  explode(";", $searchArray['price_range']);
                $min_price = $price_range[0];
                $max_price = $price_range[1];
            }
            // if (isset($searchArray['basis_type']) && $searchArray['basis_type'] == 'Hourly') {
            //     $query->whereHas('detail', function ($subQuery) use ($min_price, $max_price) {
            //         $subQuery->whereBetween('per_hour', [$min_price, $max_price]);
            //     });
            // } elseif (isset($searchArray['basis_type']) && $searchArray['basis_type'] == 'Daily') {
            //     $query->whereHas('detail', function ($subQuery) use ($min_price, $max_price) {
            //         $subQuery->whereBetween('per_day', [$min_price, $max_price]);
            //     });
            // }
            if (isset($searchArray['listing_types']) && !empty($searchArray["listing_types"])) {
                $listing_types = $searchArray["listing_types"];
                $query->whereIn('type_id', $listing_types);
            }
            if (isset($searchArray['passenger']) && !empty($searchArray['passenger'])) {
                $passenger = $searchArray['passenger'];
                $query->whereHas('detail', function ($subQuery) use ($passenger) {
                    $subQuery->where('capacity', '>=', $passenger);
                });
            }
            if (isset($searchArray['boat_length']) && !empty($searchArray['boat_length'])) {
                $boat_length = $searchArray['boat_length'];
                if ($boat_length == 'any') {
                    $query->whereHas('detail', function ($subQuery) {
                        $subQuery->whereNotNull('boat_length');
                    });
                } else {
                    $query->whereHas('detail', function ($subQuery) use ($boat_length) {
                        $subQuery->where('boat_length', '>=', $boat_length);
                    });
                }
            }
            if (isset($searchArray['amenities']) && !empty($searchArray['amenities'])) {
                $amenities = $searchArray['amenities'];
                $query->whereHas('amenity_detail', function ($subQuery) use ($amenities) {
                    // Ensure we check for any of the selected language IDs
                    $subQuery->whereIn('amenity_id', $amenities);
                });
            }

            if (isset($searchArray["ratings"]) && !empty($searchArray["ratings"])) {
                $rating = $searchArray["ratings"];
                $query->whereHas('reviews', function ($subQuery) use ($rating) {
                    $subQuery->where('rating', '>=', $rating);
                });
            }
            // if (isset($searchArray["passenger"]) && !empty($searchArray["passenger"])) {
            //     $total_guests = $searchArray["passenger"] ?? 0;

            //     // Check if date variables exist and have values
            //     $hasValidDates = (isset($startDate) && isset($endDate) && !empty($startDate) && !empty($endDate));

            //     if ($hasValidDates) {
            //         // If dates are provided, subtract overlapping bookings
            //         $query->whereHas('detail', function ($subQuery) use ($total_guests, $startDate, $endDate) {
            //             $subQuery->whereRaw('
            //                 capacity >= ? + COALESCE((
            //                     SELECT SUM(bd.adult_number + bd.child_number)
            //                     FROM bookings b
            //                     JOIN booking_details bd ON b.id = bd.booking_id
            //                     WHERE b.listing_id = listings.id
            //                     AND b.status NOT IN (3, 7)
            //                     AND (
            //                         (b.check_in BETWEEN ? AND ?) OR 
            //                         (b.check_out BETWEEN ? AND ?) OR
            //                         (b.check_in <= ? AND b.check_out >= ?) OR
            //                         (? BETWEEN b.check_in AND b.check_out) OR
            //                         (? BETWEEN b.check_in AND b.check_out)
            //                     )
            //                 ), 0)
            //             ', [
            //                 $total_guests,
            //                 $startDate,
            //                 $endDate,
            //                 $startDate,
            //                 $endDate,
            //                 $startDate,
            //                 $endDate,
            //                 $startDate,
            //                 $endDate
            //             ]);
            //         });
            //     } else {
            //         // If no dates provided, just check booking capacity >= total guests
            //         $query->whereHas('detail', function ($subQuery) use ($total_guests) {
            //             $subQuery->where('booking_capacity', '>=', $total_guests);
            //         });
            //     }
            // }
        }
        // watercraft end
        // for car start
        if (isset($searchArray['category_id']) && in_array($searchArray['category_id'], [3])) {

            if (isset($searchArray['price_range']) && !empty($searchArray['price_range'])) {
                $price_range =  explode(";", $searchArray['price_range']);
                $min_price = $price_range[0];
                $max_price = $price_range[1];
            }
            if (isset($searchArray['basis_type']) && $searchArray['basis_type'] == 'Hourly') {
                $query->whereHas('detail', function ($subQuery) use ($min_price, $max_price) {
                    $subQuery->whereBetween('per_hour', [$min_price, $max_price]);
                });
            } elseif (isset($searchArray['basis_type']) && $searchArray['basis_type'] == 'Daily') {
                $query->whereHas('detail', function ($subQuery) use ($min_price, $max_price) {
                    $subQuery->whereBetween('per_day', [$min_price, $max_price]);
                });
            }

            if (isset($searchArray['listing_types']) && !empty($searchArray["listing_types"])) {
                $listing_types = $searchArray["listing_types"];
                $query->whereIn('type_id', $listing_types);
            }
            if (isset($searchArray['seats']) && !empty($searchArray['seats'])) {
                $seats = $searchArray['seats'];
                $query->whereHas('detail', function ($subQuery) use ($seats) {
                    $subQuery->where('seats', $seats);
                });
            }

            if (isset($searchArray['transmission']) && !empty($searchArray['transmission'])) {
                $transmission = $searchArray['transmission'];
                $query->whereHas('detail', function ($subQuery) use ($transmission) {
                    $subQuery->whereIn('transmission', $transmission);
                });
            }
            //for car
            if (isset($searchArray['engine_type']) && !empty($searchArray['engine_type'])) {
                $engine_types = $searchArray['engine_type']; // This will be an array if multiple checkboxes are selected
                $query->whereHas('detail', function ($subQuery) use ($engine_types) {
                    $subQuery->whereIn('engine_type', $engine_types); // Use whereIn to match any of the selected engine types
                });
            }
            if (isset($searchArray["ratings"]) && !empty($searchArray["ratings"])) {
                $rating = $searchArray["ratings"];
                $query->whereHas('reviews', function ($subQuery) use ($rating) {
                    $subQuery->where('rating', $rating);
                });
            }
            if (isset($searchArray["pickUp"]) && !empty($searchArray["pickUp"]) && isset($searchArray["dropOff"]) && !empty($searchArray["dropOff"])) {
                $pickupTime = $searchArray["pickUp"];
                $dropOffTime = $searchArray["dropOff"];
                $query->whereHas('detail', function ($subQuery) use ($startDate, $endDate, $pickupTime, $dropOffTime) {

                    // For hourly listings, check for available slots within the given time range
                    $subQuery->whereHas('listing.hourly_availabilities', function ($hourSubQuery) use ($pickupTime, $dropOffTime) {
                        $hourSubQuery->whereRaw('start_time <= ? AND end_time >= ?', [$dropOffTime, $pickupTime]);
                    });

                    // For daily listings, check for booking availability by date range and status
                    $subQuery->where(function ($query) use ($startDate, $endDate) {
                        $query->whereNotIn('status', [3, 7])  // Exclude completed (3) and cancelled (7) bookings
                            ->whereRaw('NOT EXISTS (SELECT 1 
                    FROM bookings b
                    JOIN booking_details bd ON b.id = bd.booking_id
                    WHERE b.listing_id = listings.id
                    AND b.status NOT IN (3, 7)
                    AND (
                        (b.check_in BETWEEN ? AND ?) OR
                        (b.check_out BETWEEN ? AND ?) OR
                        (b.check_in <= ? AND b.check_out >= ?) OR
                        (? BETWEEN b.check_in AND b.check_out) OR
                        (? BETWEEN b.check_in AND b.check_out)
                    )
                )', [
                                $startDate,
                                $endDate,
                                $startDate,
                                $endDate,
                                $startDate,
                                $endDate,
                                $startDate,
                                $endDate
                            ]);
                    });
                });
            }
        }
        // car end
        // if (isset($searchArray['types']) && !empty($searchArray["types"])) {
        //     $resident_types = $searchArray["types"];
        //     $query->whereIn('type', $resident_types);
        // }
        // if (isset($searchArray["daterange"]) && !empty($searchArray["daterange"])) {
        //     // Get the selected date range from the search array
        //     $date_range = $searchArray["daterange"];
        //     // Convert the date range from 'd-M-Y' to 'Y-m-d'
        //     $dates = explode(" - ", $date_range);
        //     $start_date = \Carbon\Carbon::createFromFormat('d-M-Y', $dates[0])->format('Y-m-d');
        //     $end_date = \Carbon\Carbon::createFromFormat('d-M-Y', $dates[1])->format('Y-m-d');
        //     // Apply the query filtering the listings based on status, pause, and booking dates
        //     $query->where('status', 1) // Ensure listing status is active
        //           ->where('pause', 0)  // Ensure listing is not paused
        //           ->whereDoesntHave('searchbookings', function ($subQuery) use ($start_date, $end_date) {
        //               // Exclude bookings with status 0, 1, 3 and where check-in or check-out overlap with the date range
        //               $subQuery->whereNotIn('status', [0, 1, 3])
        //                        ->where(function ($query) use ($start_date, $end_date) {
        //                            // Ensure booking check-in and check-out don't overlap with the search date range
        //                            $query->whereBetween('check_in', [$start_date, $end_date])
        //                                  ->orWhereBetween('check_out', [$start_date, $end_date]);
        //                        });
        //           });
        // }
        // if (isset($searchArray["dateExp"]) && !empty($searchArray["dateExp"])) {
        //     // Get the selected date range from the search array
        //     $date = $searchArray["dateExp"];
        //     $start_date = \Carbon\Carbon::createFromFormat('d-M-Y', $date)->format('Y-m-d');
        //     $end_date = \Carbon\Carbon::createFromFormat('d-M-Y', $date)->format('Y-m-d');
        //     // Apply the query filtering the listings based on status, pause, and booking dates
        //     $query->where('status', 1) // Ensure listing status is active
        //           ->where('pause', 0)  // Ensure listing is not paused
        //           ->whereDoesntHave('searchbookings', function ($subQuery) use ($start_date, $end_date) {
        //               // Exclude bookings with status 0, 1, 3 and where check-in or check-out overlap with the date range
        //               $subQuery->whereNotIn('status', [0, 1, 3])
        //                        ->where(function ($query) use ($start_date, $end_date) {
        //                            // Ensure booking check-in and check-out don't overlap with the search date range
        //                            $query->whereBetween('check_in', [$start_date, $end_date])
        //                                  ->orWhereBetween('check_out', [$start_date, $end_date]);
        //                        });
        //           });
        // }
        // Final query execution
        return $query->where("status", 1)->active()->orderBy("id", "DESC")->get();
    }

    //apply weighted ranking
    function apply_weighted_ranking($listings, $search_type, $current_lat, $current_lng)
    {
        $criteria = [];
        if ($search_type === 'current_location') {
            $criteria = [
                'proximity' => 25,
                'click_through_rate' => 5,
                'bookings' => 20,
                'favorites' => 10,
                'price_competitiveness' => 25,
                'rating' => 15,
            ];
        } elseif ($search_type === 'specific_location') {
            $criteria = [
                'click_through_rate' => 10,
                'bookings' => 20,
                'favorites' => 10,
                'price_competitiveness' => 35,
                'rating' => 25,
            ];
        }
        // Preload required data (calculate dynamic fields if necessary)
        $listings = $listings->map(function ($listing) {
            $listing->bookings = $listing->bookings()->count(); // Count bookings
            $listing->favorites = $listing->wishlists()->count(); // Count favorites
            $listing->rating = $listing->reviews()->avg('rating') ?? 0; // Average rating
            // Determine price based on basis_type
            $basisType = $listing->detail->basis_type ?? null;
            if ($basisType === 'Hourly') {
                $listing->price = $listing->detail->per_hour ?? null;
            } elseif ($basisType === 'Daily') {
                $listing->price = $listing->detail->per_day ?? null;
            } elseif ($basisType === 'Tour') {
                $listing->price = $listing->detail->adult_price ?? null;
            } else {
                $listing->price = null;
            }
            return $listing;
        });
        // Calculate average price
        $average_price = $this->calculate_average($listings, 'price');
        foreach ($listings as &$listing) {
            $score = 0;
            $details = [];
            // 1. Proximity (only for Current Location searches)
            $proximity_score = 0;
            if ($search_type === 'current_location' && isset($listing->lat, $listing->lng)) {
                $distance = $this->calculate_distance($current_lat, $current_lng, $listing->lat, $listing->lng);
                if ($distance <= 15) {
                    $proximity_score = $criteria['proximity']; // Full weightage for listings ≤ 15 km
                } elseif ($distance <= 50) {
                    $proximity_score = $criteria['proximity'] * (1 - ($distance - 15) / 35); // Proportional weightage for listings between 15 km and 50 km
                }
            }
            $score += $proximity_score;
            $details['proximity'] = round($proximity_score);
            // 2. High Click-Through Rate
            $ctr_score = 0;
            $ctr_threshold = $this->calculate_percentile($listings, 'click_through_rate', 80);
            if (isset($listing->click_through_rate) && $listing->click_through_rate >= $ctr_threshold) {
                $ctr_score = $criteria['click_through_rate'];
            }
            $score += $ctr_score;
            $details['click_rate'] = round($ctr_score);
            // 3. Bookings
            $booking_score = 0;
            $booking_threshold = $this->calculate_percentile($listings, 'bookings', 80);
            if (isset($listing->bookings) && $listing->bookings >= $booking_threshold) {
                $booking_score = $criteria['bookings'];
            }
            $score += $booking_score;
            $details['bookings'] = round($booking_score);
            // 4. Favorites
            $favourite_score = 0;
            $favorites_threshold = $this->calculate_percentile($listings, 'favorites', 80);
            if (isset($listing->favorites) && $listing->favorites >= $favorites_threshold) {
                $favourite_score  = $criteria['favorites'];
            }
            $score += $favourite_score;
            $details['favorites'] = round($favourite_score);
            //$details['favorites'] = round(($favourite_score / $criteria['favorites']) * 100); // Convert to percentage
            // 5. Price Competitiveness
            $pricing_score = 0;
            if (isset($listing->price) && $listing->price <= $average_price) {
                $pricing_score = $criteria['price_competitiveness'];
            }
            $score += $pricing_score;
            $details['pricing'] = round($pricing_score);
            // 6. Listing Rating
            $rating_score = 0;
            if (isset($listing->rating) && $listing->rating >= 4.8) {
                $rating_score = $criteria['rating'];
            }
            $score += $rating_score;
            $details['rating'] = round($rating_score);
            // Store total score and details
            $listing->score = round($score);
            $listing->details = $details; // Store individual weightage
        }
        // Sort listings by score in descending order
        $listings = $listings->sortByDesc('score');
        return $listings;
    }
    //calculate price avaerage
    function calculate_average($listings, $field)
    {
        $values = $listings->pluck($field)->filter();
        return $values->count() > 0 ? $values->sum() / $values->count() : 0;
    }
    //calculate distance
    // function calculate_distance($lat1, $lng1, $lat2, $lng2)
    // {
    //     $earth_radius = 6371; // Earth radius in KM
    //     $lat_diff = deg2rad($lat2 - $lat1);
    //     $lng_diff = deg2rad($lng2 - $lng1);
    //     $a = sin($lat_diff / 2) * sin($lat_diff / 2) +
    //         cos(deg2rad($lat1)) * cos(deg2rad($lat2)) *
    //         sin($lng_diff / 2) * sin($lng_diff / 2);
    //     $c = 2 * atan2(sqrt($a), sqrt(1 - $a));
    //     $distance = $earth_radius * $c;
    //     return $distance; // Distance in KM
    // }
    function calculate_distance($origin_lat, $origin_lng, $destination_lat, $destination_lng)
    {
        $apiKey = google_map_key(); // Replace with your actual API key
        // Construct the API URL
        $url = "https://maps.googleapis.com/maps/api/distancematrix/json?origins={$origin_lat},{$origin_lng}&destinations={$destination_lat},{$destination_lng}&key={$apiKey}";
        // Use Guzzle or Http to make the API request
        $response = Http::get($url); // Using Laravel's Http client
        if ($response->successful()) {
            $data = $response->json();

            if (isset($data['rows'][0]['elements'][0]['distance']['value'])) {
                // Distance is returned in meters, convert to kilometers
                $distance_in_km = $data['rows'][0]['elements'][0]['distance']['value'] / 1000;
                return $distance_in_km;
            } else {
                throw new \Exception('Google API did not return a valid distance.');
            }
        } else {
            throw new \Exception('Failed to connect to Google Distance Matrix API.');
        }
    }

    function calculate_percentile($listings, $field, $percentile)
    {
        $values = collect($listings)->pluck($field)->filter()->sort()->values();
        $index = (int) round(($percentile / 100) * $values->count()) - 1;
        return $values[$index] ?? 0;
    }
    public function deleteListing($id)
    {
        DB::beginTransaction();
        try {
            $gallery_images = listingGallery::where("listing_id", $id)->pluck('url')->toArray();
            if (!empty($gallery_images)) {
                Storage::disk('website')->delete($gallery_images);
            }
            $active_bookings = $this->bookingService->activeBookings("listing", $id);
            $full_refund_booking = $this->bookingService->full_refund_booking($active_bookings);
            if ($full_refund_booking["status"] == false) {
                return api_response(false, $full_refund_booking["message"]);
            }
            $relations = [
                ListingAddress::class,
                ListingAmenity::class,
                ListingAttribute::class,
                ListingDetail::class,
                ListingDiscount::class,
                ListingExperience::class,
                ListingFeatureItinerary::class,
                ListingGallery::class,
                ListingHourlyAvailability::class,
                ListingRestrictedDay::class,
                ListingRule::class,
                ListingSeasonPrice::class,
                ListingTourLanguage::class,
                ListingTourDuration::class,
                Wishlist::class,
            ];
            foreach ($relations as $relation) {
                $relation::where("listing_id", $id)->delete();
            }
            Listing::destroy($id);
            DB::commit();
            return api_response(true, "Listing Deleted");
        } catch (\Exception $e) {
            DB::rollback();
            return api_response(false, $e->getMessage());
        }
    }
    function multiple_listing(array $listing_ids)
    {
        return Listing::with("active_bookings")->whereIn("ids", $listing_ids)->get();
    }
    function pauseListing($id)
    {
        try {
            $listing = Listing::where('ids', $id)->firstOrFail();
            $listing->pause = !$listing->pause; // Toggle pause status
            $listing->save();
            $message = $listing->pause ? 'Listing Paused' : 'Listing Unpaused';
            if ($listing->pause == 1) {
                Mail::to($listing->user->email)->send(new ListingPauseMail($listing->user, $listing));
            }
            return api_response(true, $message, $listing);
        } catch (\Exception $e) {
            return api_response(false, $e->getMessage(), null);
        }
    }
    function singleListing($ids): Listing
    {
        $listing = Listing::where("ids", $ids)->first();
        return $listing;
    }
}
