<?php

namespace App\Http\Controllers\Api;

use App\Amenity;
use App\Category;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\ListingRequest;
use App\Http\Requests\StoreMediaRequest;
use App\Listing;
use App\listingGallery;
use App\Services\ListingService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Validator;

class ListingController extends Controller
{
    function __construct(
        protected ListingService $listingService
    ){}

    function listing_consent()
    {
        if (auth()->user()->listing_consent) {
            return api_response_json(false, "consent already given");
        } else {
            auth()->user()->update(["listing_consent" => 1]);
            return api_response_json(true, "consent given");
        }
    }
    function get_category_listings(Request $request)
    {
        $request->validate([
            "category_id" => "required|exists:categories,ids"
        ]);
        try{
            $category = Category::where("ids", $request->category_id)->firstOrFail();
            $listings = $this->listingService->getListings($category->id);
            return api_response_json(true, "listing found", $listings);
        }catch(\Exception $e){
            return api_response_json(false, $e->getMessage());
        }
    }
    function get_listings_by_name(Request $request)
    {
        $request->validate([
            "listing_name" => "required"
        ]);
        try{
            $listings = $this->listingService->getListingsByName($request->listing_name);
            if($listings->isEmpty()){
                return api_response_json(false, "No listings found");
            }
            return api_response_json(true, "listing found", $listings);
        }catch(\Exception $e){
            return api_response_json(false, $e->getMessage());
        }
    }
    function listing_detail($locale, $listing_id){
        try {
            $listing = Listing::where("ids", $listing_id)->active()->firstOrFail();
            $result = $this->listingService->getListingDetailWithAvailability($listing->slug, $listing->ids);

            if ($result["status"] != true) {
                return api_response_json(false, $result["message"]);
            }

            return api_response_json(true, "Listing detail with availability found", $result["data"]);
        } catch (\Exception $e) {
            return api_response_json(false, $e->getMessage());
        }
    }


    function short_url($locale, $listing_id){
        $listing = Listing::where("ids", $listing_id)->active()->firstOrFail();
        return $this->listingService->url_code($listing->ids);
    }
    function my_listing(Request $request)
    {
        try{
            // cache the listing 
            // $cacheKey = 'my_listings_' . auth()->id();
            // $listings = Cache::remember($cacheKey, 10, function () use ($request) {
                $keyword = $request->get('search');
                $filter = $request->get('filter', '0');
                $limit = $request->get('per_page', 10);
                $listings = $this->listingService->my_listing($keyword, $filter, $limit);
            // });
            return api_response_json(true, "listing found", $listings);
        }catch(\Exception $e){
            return api_response_json(false, $e->getMessage());
        }
    }
    function create_listing($locale, $category_id)
    {
        $cacheKey = 'category_' . $category_id;
        // $category = Cache::remember($cacheKey, 10, function () use ($category_id) {
        //     return Category::with("cms_steppers")->where("ids", $category_id)->first();
        // });
        $category = Cache::remember($cacheKey, 10, function () use ($category_id, $locale) {
            return Category::with([
                "cms_steppers" => function ($query) use ($locale) {
                    $query->withTranslation($locale);
                },
                "listing_types" => function ($query) use ($locale) {
                    $query->withTranslation($locale);
                },
                "amenities.options" => function ($query) use ($locale) {
                    $query->withTranslation($locale);
                },
                "tour_languages"
            ])->where("ids", $category_id)->first();
        });
        $category["cancellation_policy"] = [
            [
                "title" => "Flexible",
                "sub_title" => [
                    translate('stepper.cancellation_flexible_1'),
                    translate('stepper.cancellation_flexible_2')
                ]
            ],
            [
                "title" => "Moderate",
                "sub_title" => [
                    translate('stepper.cancellation_moderate_1'),
                    translate('stepper.cancellation_moderate_2'),
                    translate('stepper.cancellation_moderate_3')
                ]
            ],
            [
                "title" => "Strict",
                "sub_title" => [
                    translate('stepper.cancellation_strict_1'),
                    translate('stepper.cancellation_strict_2'),
                    translate('stepper.cancellation_strict_3')
                ]
            ]
        ];

        if ($category->id == 3 || $category->id == 2) {
            if ($category->id == 3) {
                $category["engine_type"] = [
                    [
                        "title" => translate('stepper.petrol'),
                        "image" => "images/diesel_engine.png"
                    ],
                    [
                        "title" => translate('stepper.diesel'),
                        "image" => "images/diesel_engine.png"
                    ],
                    [
                        "title" => translate('stepper.hybrid'),
                        "image" => "images/hybrid_engine.png"
                    ],
                    [
                        "title" => translate('stepper.electric'),
                        "image" => "images/electric_engine.png"
                    ],
                    [
                        "title" => translate('stepper.hydrogen'),
                        "image" => "images/hydrogen_engine.png"
                    ],
                    [
                        "title" => translate('stepper.no_engine'),
                        "image" => "images/no_engine.png"
                    ],
                    [
                        "title" => translate('stepper.other'),
                        "image" => "images/other_engine.png"
                    ]
                ];

                $category["transmission"] = [
                    [
                        "title" => translate('stepper.manual'),
                        "image" => "images/manual_transmission.png"
                    ],
                    [
                        "title" => translate('stepper.automatic'),
                        "image" => "images/automatic_transmission.png"
                    ],
                    [
                        "title" => translate('stepper.cvt'),
                        "image" => "images/cvt_transmission.png"
                    ],
                    [
                        "title" => translate('stepper.dct'),
                        "image" => "images/dct_transmission.png"
                    ],
                    [
                        "title" => translate('stepper.semi_automatic'),
                        "image" => "images/semi_auto_transmission.png"
                    ],
                    [
                        "title" => translate('stepper.no_transmission'),
                        "image" => "images/not_allowed_icon.png"
                    ],
                    [
                        "title" => translate('stepper.other'),
                        "image" => "images/other_transmission.png"
                    ]
                ];
            }

            $category["hours"] = [
                [
                    "image" => "12_am.png",
                    "value" => "00:00 - 01:00"
                ],
                [
                    "image" => "1_am.png",
                    "value" => "01:00 - 02:00"
                ],
                [
                    "image" => "2_am.png",
                    "value" => "02:00 - 03:00"
                ],
                [
                    "image" => "3_am.png",
                    "value" => "03:00 - 04:00"
                ],
                [
                    "image" => "4_am.png",
                    "value" => "04:00 - 05:00"
                ],
                [
                    "image" => "5_am.png",
                    "value" => "05:00 - 06:00"
                ],
                [
                    "image" => "6_am.png",
                    "value" => "06:00 - 07:00"
                ],
                [
                    "image" => "7_am.png",
                    "value" => "07:00 - 08:00"
                ],
                [
                    "image" => "8_am.png",
                    "value" => "08:00 - 09:00"
                ],
                [
                    "image" => "9_am.png",
                    "value" => "09:00 - 10:00"
                ],
                [
                    "image" => "10_am.png",
                    "value" => "10:00 - 11:00"
                ],
                [
                    "image" => "11_am.png",
                    "value" => "11:00 - 12:00"
                ],
                [
                    "image" => "12_pm.png",
                    "value" => "12:00 - 13:00"
                ],
                [
                    "image" => "1_pm.png",
                    "value" => "13:00 - 14:00"
                ],
                [
                    "image" => "2_pm.png",
                    "value" => "14:00 - 15:00"
                ],
                [
                    "image" => "3_pm.png",
                    "value" => "15:00 - 16:00"
                ],
                [
                    "image" => "4_pm.png",
                    "value" => "16:00 - 17:00"
                ],
                [
                    "image" => "5_pm.png",
                    "value" => "17:00 - 18:00"
                ],
                [
                    "image" => "6_pm.png",
                    "value" => "18:00 - 19:00"
                ],
                [
                    "image" => "7_pm.png",
                    "value" => "19:00 - 20:00"
                ],
                [
                    "image" => "8_pm.png",
                    "value" => "20:00 - 21:00"
                ],
                [
                    "image" => "9_pm.png",
                    "value" => "21:00 - 22:00"
                ],
                [
                    "image" => "10_pm.png",
                    "value" => "22:00 - 23:00"
                ],
                [
                    "image" => "11_pm.png",
                    "value" => "23:00 - 00:00"
                ]
            ];
            $category["days"] = [
                [
                    "image" => "mon_icon.png",
                    "value" => "Monday"
                ],
                [
                    "image" => "tue_icon.png",
                    "value" => "Tuesday"
                ],
                [
                    "image" => "wed_icon.png",
                    "value" => "Wednesday"
                ],
                [
                    "image" => "thu_icon.png",
                    "value" => "Thursday"
                ],
                [
                    "image" => "fri_icon.png",
                    "value" => "Friday"
                ],
                [
                    "image" => "sat_icon.png",
                    "value" => "Saturday"
                ],
                [
                    "image" => "sun_icon.png",
                    "value" => "Sunday"
                ]
            ];
        }

        if ($category) {
            return api_response_json(true, "category found", $category);
        } else {
            return api_response_json(false, "category not found", null);
        }
    }

    function store_update_listing(ListingRequest $request, $locale, $category_ids, $listing_id = null)
    {
        $category = Category::where("ids", $category_ids)->firstOrFail();
        $request_data = $request->validated();
        $request_data["listing_id"] = $listing_id;
        if($listing_id == null){
            $listing = Listing::where("user_id", auth()->id())->where("category_id", $category->id)->latest()->first();
            // check the latest listing creation 5 min to prevent multiple creating
            if($listing){
                $created_at = $listing->created_at;
                $diff = \Carbon\Carbon::now()->diffInMinutes($created_at);
                if($diff < 1){
                    return api_response_json(true, "You can't create multiple listing in 1 minutes. Please wait for 5 minutes. or update last created listing", $listing);
                }
            }
        }
        $request_data["name"] = $request->input("name", "Mobile Testing (DONT TEST on this listing)");
        $request_data["category_id"] = $category->id;
        return $this->listingService->create_update($request_data, $category->id);
    }

    public function listingMedia(StoreMediaRequest $request, $locale, $listing_id)
    {
        try{

            $listing = Listing::where("ids", $listing_id)->firstOrFail();
    
            $existingMediaCount = listingGallery::where('listing_id', $listing->id)->where("type", $request->type)->count();
    
            $newMediaCount = is_array($request->media) ? count($request->media) : 0;
            $totalMediaCount = $existingMediaCount + $newMediaCount;
            if ($request->type == "image" && $totalMediaCount > 100) {
                return api_response_json(false, 'You can upload a maximum of 100 ' . $request->type . 's in total.');
            } elseif ($request->type == "file" && $totalMediaCount > 10) {
                return api_response_json(false, 'You can upload a maximum of 10 ' . $request->type . 's in total.');
            }
    
            $currentMaxSortOrder = listingGallery::where('listing_id', $listing->id)
                ->where("type", $request->type)
                ->max('sort_order') ?? 0;
    
            $isImageType = $request->type === "image";
    
            if ($isImageType) {
                $hasCover = listingGallery::where('listing_id', $listing->id)
                    ->where('is_cover', 1)
                    ->exists();
            }
    
            $mediaPaths = [];
    
            foreach ($request->media as $key => $media) {
                $listingGallery = new listingGallery();
                $listingGallery->listing_id = $listing->id;
                $listingGallery->name = $media->getClientOriginalName();
                $listingGallery->type = $request->type;
                $listingGallery->url = $this->storeImageToWebP("listing-" . $request->type, $media);
                $listingGallery->is_cover = ($isImageType && !$hasCover && $key === 0) ? 1 : 0;
                $listingGallery->sort_order = $isImageType ? $currentMaxSortOrder + $key + 1 : 0;
                $listingGallery->save();
    
                $mediaPaths[] = $listingGallery;
            }
    
            return api_response_json(true, ucfirst($request->type) . "s uploaded successfully", $mediaPaths);
        }catch(\Exception $e){
            return api_response_json(false, $e->getMessage());
        }
    }
    public function duplicate_listing($locale, $listing_id)
    {
        try{
            $listing = Listing::where("ids", $listing_id)->where("ids", $listing_id)->where("status", 1)->firstOrFail();
            $request_data["category_id"] = $listing->category_id;
            $request_data["original_listing_id"] = $listing->id;
            $request_data["name"] = $listing->name . " (Copy)";
            $request_data["current_step"] = 1;
            return $this->listingService->duplicate_listing($request_data, $request_data["category_id"]);
        }catch(\Exception $e){
            return api_response_json(false, $e->getMessage());
        }
    }
    function imageDelete(Request $request)
    {
        $validator = Validator::make($request->all(), [
            "listing_id" => "required|exists:listings,ids",
            "image_id" => "required|exists:listing_galleries,id",
        ], [
            "listing_id.exists" => "Listing does not exist",
            "image_id.exists" => "Image does not exist"
        ]);
        if ($validator->fails()) {
            return api_response_json(false, $validator->errors()->first());
        }
        try {
            $listing = Listing::where("ids", $request->listing_id)->where("user_id", auth()->id())->first();
            if (!$listing) {
                return api_response_json(false, "Listing does not exist");
            }
            $listing_gallery = ListingGallery::where("id", $request->image_id)->where("listing_id", $listing->id)->firstOrFail();
            $type = $listing_gallery->type;
            if ($listing_gallery->is_cover == 1) {
                $new_cover = listingGallery::where("listing_id", $listing->id)->where("is_cover", "!=", 1)->where('type', 'image')->orderBy("id", "ASC")->first();
                if (isset($new_cover)) {
                    $new_cover->is_cover = 1;
                    $new_cover->save();
                }
            }
            $this->deleteImage($listing_gallery->url);
            $listing_gallery->delete();

            $listing_images = ListingGallery::where('listing_id', $listing->id)->where('type', $type)->get();
            return api_response_json(true, "Image deleted successfully", [
                "images" => $listing_images
            ]);
        } catch (\Exception $e) {
            return api_response_json(false, $e->getMessage());
        }
    }
    function sortingImage(Request $request)
    {
        $validator = Validator::make($request->all(), [
            "image_ids" => "required|array",
            "listing_id" => "required|exists:listings,ids",
        ], [
            "listing_id.exists" => "Listing does not exist",
        ]);
        if ($validator->fails()) {
            return api_response_json(false, $validator->errors()->first());
        }
        try {
            $sorting_img = $this->listingService->sortingImage($request->image_ids, $request->listing_id);
            if ($sorting_img["status"] == false) {
                return api_response_json(false, $sorting_img["message"]);
            }
            $listing_images = ListingGallery::where('listing_id', $request->listing_id)->where('type', 'image')->get();
            return api_response_json(true, "Sort order updated successfully", [
                "images" => $listing_images
            ]);
        } catch (\Exception $e) {
            return api_response_json(false, $e->getMessage());
        }
    }
    function coverImage(Request $request)
    {
        $validator = Validator::make($request->all(), [
            "listing_id" => "required|exists:listings,ids",
            "image_id" => "required|exists:listing_galleries,id",
        ], [
            "listing_id.exists" => "Listing does not exist",
            "image_id.exists" => "Image does not exist"
        ]);
        if ($validator->fails()) {
            return api_response_json(false, $validator->errors()->first());
        }
        try {
            $cover_img = $this->listingService->coverImage($request->image_id, $request->listing_id);
            if ($cover_img["status"] == false) {
                return api_response_json(false, $cover_img["message"]);
            }
            $listing = Listing::where("ids", $request->listing_id)->first();
            $listing_images = ListingGallery::where('listing_id', $listing->id)->where('type', 'image')->get();
            return api_response_json(true, "Cover image updated successfully", [
                "images" => $listing_images
            ]);
        } catch (\Exception $e) {
            return api_response_json(false, $e->getMessage());
        }
    }
    function show_listing($locale, $ids){
        try{
            $listing = $this->listingService->listing_show($ids);
            return api_response_json(true, "Listing found", $listing);
        }catch(\Exception $e){
            return api_response_json(false, $e->getMessage());
        }
    }
    function internal_name(Request $request)
    {
        $request->validate([
            "listing_ids" => "required",
            "internal_name" => "required"
        ]);
        $listing = Listing::where("ids", $request->listing_ids)->where("user_id", auth()->id())->firstOrFail();
        if($listing->status != 1){
            return api_response(false, "You can't update internal name which are not active");
        }
        $listing->internal_name = $request->internal_name;
        $listing->save();
        return api_response(true, "Internal name saved", $listing);
    }
    function pause_listing($locale, $ids)
    {
        try {
            $listing = Listing::where("ids", $ids)->where("user_id", auth()->id())->firstOrFail();
            return $this->listingService->pauseListing($listing->ids);
        } catch (\Throwable $th) {
            return api_response(false, $th->getMessage());
        }
    }
    function delete_listing($locale, $ids)
    {
        try {
            $listing = Listing::where("ids", $ids)->where("user_id", auth()->id())->firstOrFail();
            return $this->listingService->deleteListing($listing->id);
        } catch (\Throwable $th) {
            return api_response(false, $th->getMessage());
        }
    }
    function image_upload(Request $request)
    {
        // imageNames
        $validator = Validator::make($request->all(), [
            "listing_images" => "required|array"
        ]);
        if ($validator->fails()) {
            return api_response(false, $validator->errors()->first());
        }
        $image_names = [];
        foreach ($request->listing_images as $value) {
            $image_names[] = $this->storeImage("listing-images", $value);
        }
        $string = implode(";", $image_names);
        return ["listing_image" => $string];
    }
}
