<?php

use App\Http\Controllers\{
    Review<PERSON>ontroller,
    BookingController,
    CalendarController,
    CmsController,
    ContactController,
    Dashboard\ListingController,
    DashboardController,
    FaqController,
    LanguagesController,
    MailSettingController,
    UsersController,
    SuspendController,
    WebsiteController,
    HelpCenter\HelpCenterController,
    WithdrawalRequestController,
};
use Illuminate\Support\Facades\Route;
use Illuminate\Http\Request;

// Admin routes
Route::group(['middleware' => ['auth', 'roles', 'last_active'], 'roles' => ['admin', "sub_admin", 'user', 'service', 'customer']], function () {
    Route::post("bulk-listing-delete", [ListingController::class, "bulk_delete"])->name("listing_bulk_delete");
    Route::get("get-listing-booking", [ListingController::class, "listing_booking"])->name("get_listing_booking");
    Route::get("pause-listing/{id}", [ListingController::class, "listing_pause"])->name("listing_pause");
    Route::get("get-listing-documents/{listing_ids}", [ListingController::class, "get_listing_documents"])->name("get_listing_documents");
    Route::post("listing-internal-name", [ListingController::class, "internal_name"])->name("internal_name");
    Route::get('download-listing-assets-zip/{listingId}', [ListingController::class, 'downloadListingAssetsZip'])->name('download_listing_assets_zip');
    Route::get("generate-url-short/{listing_id}", [ListingController::class, "short_generate_link"])->name("listing.generate_link")->middleware("ajax");

    Route::middleware("ajax")->group(function () {
        Route::get("listing-active-booking/{id}", [ListingController::class, "listing_active_booking"])->name("listing_active_booking");
        Route::post("listingMedia", [ListingController::class, "listingMedia"])->name("listingMedia");
        Route::delete("listing-image-delete", [ListingController::class, "listing_image_delete"])->name("listing_image_delete");
        Route::post("listing-cover-image", [ListingController::class, "listing_cover_image"])->name("listing_cover_image");
        Route::post("listing-photo-order", [ListingController::class, "listing_photo_order"])->name("listing_photo_order");
        Route::get("detail-slide/{id}/{category}", [WebsiteController::class, "detailSlide"])->name('detail_slide');

        // reviews
        Route::get("review-get/{listing_id}", [ReviewController::class, "review_get"])->name("review_get")->middleware("ajax");
    });
    Route::post("review_update", [ReviewController::class, "review_update"])->name("review_update");
    Route::get("delete-review-reply/{id}", [ReviewController::class, "delete_review_reply"])->name("delete_review_reply");

    Route::post("review-reply/{id}", [ReviewController::class, "review_reply"])->name("review.reply");
    Route::get("review-filter", [ReviewController::class, "review_filter"])->name("review_filter")->middleware("ajax");
    Route::get("get-provider-listings", [ReviewController::class, "getProviderListings"])->name("get_provider_listings")->middleware("ajax");

    Route::get('language', [LanguagesController::class, "index"])->name("language");
    Route::post('translations/update', [LanguagesController::class, "transUpdate"])->name('translation.update.json');
    Route::get("dashboard", [DashboardController::class, "index"]);


    Route::get('account-settings', 'UsersController@getSettings');
    Route::post('account-settings', 'UsersController@saveSettings');

    // withdrawal request
    Route::get("pay-withdrawal-req/{id}", [DashboardController::class, "pay_withdrawal_req"]);
    Route::post("withdrawal-request-reject", [DashboardController::class, "withdrawal_request_reject"]);

    // Booking
    Route::get("dashboard/inbox", [DashboardController::class, "dashboard_inbox"])->name("dashboard_inbox");


    Route::get('notifications', [DashboardController::class, "notification"])->name('Notification');
    Route::post('delete-notifications', [DashboardController::class, "delete_notifications"])->name('delete_notifications');
    Route::get('mark-as-read/{id}', [DashboardController::class, "markAsRead"])->name('markAsRead');
    Route::get('mark-all-read', [DashboardController::class, "mark_all_read"])->name('mark_all_read');
    Route::get("dashboard/inbox", [DashboardController::class, "dashboard_inbox"])->name("dashboard_inbox");

    //dashboard Land Module Route for admin,user role
     Route::middleware("user_role:admin,user,sub_admin")->group(function () {
     Route::get('/languages', [LanguagesController::class, 'index'])->name('languages.index');
    Route::post('/languages', [LanguagesController::class, 'store'])->name('languages.store');
    Route::get('/languages/objects', [LanguagesController::class, 'getObjects'])->name('languages.getObjects');
    Route::post('/languages/add-object', [LanguagesController::class, 'addObject'])->name('languages.addObject');
    Route::delete('/languages/{key}', [LanguagesController::class, 'destroy'])->name('languages.destroy');
    Route::post('translation/update', [LanguagesController::class, 'transUpdate'])->name('translation.update.json');
    Route::delete('/languages/object/{object}', [LanguagesController::class, 'deleteObject'])->name('languages.deleteObject');
    Route::delete('/languages/{keyPath}', [LanguagesController::class, 'destroy'])->name('languages.destroy');
    });
    // Service provider
    Route::middleware("user_role:user,service,sub_admin")->group(function () {

        // admin
        Route::get('e-wallet', [DashboardController::class, "eWallet"])->name('e_wallet');
        Route::get('check-is-payable', [DashboardController::class, 'checkIsPayable']);
        Route::get('/get-earning-data', [DashboardController::class, 'getEarningData'])->name('get-earning-data');
        Route::get("user-status/{id}", [DashboardController::class, "user_status"])->name("user_status");

        Route::get("category", [ListingController::class, "get_cateegory"])->name('get_category')->middleware('listingConsent');
        Route::post("consent-post", [ListingController::class, "consentPost"]);
        Route::post("listing-image-upload", [ListingController::class, "listing_image_upload"])->name("listing_image_upload");
        // Route::get("listing-image-delete/{id}", [ListingController::class, "listing_image_delete"])->name("listing_image_delete");


        // listing page
        Route::get("get-option-amenity", [ListingController::class, "get_amenity_option"])->name("get_amenity_option");
        Route::get('listings/{slug}/{listing_ids?}', [ListingController::class, "listing_add"])->name('listing_add')->middleware('listingConsent');
        Route::post("listing-create-update/{category_ids}/{listing_ids?}", [ListingController::class, "create_update"])->name('create_update');
        Route::get('duplicate-listings/{slug}/{listing_ids}', [ListingController::class, "duplicate_listing"])->name('duplicate_listing');

        // Route::post('listing-store', [ListingController::class, "listing_store"])->name('listing_store');
                Route::post('/translate-text', function(Request $request) {
             $translatedText = translateDynamic($request->text, $request->target_language);
             return response()->json(['translated_text' => $translatedText], 200, [], JSON_UNESCAPED_UNICODE);
            })->name('translate.text');

    });
});
Route::middleware("user_role:service")->group(function () {
    // calendar
    Route::prefix("calendar")->group(function(){
        Route::get('/', [CalendarController::class, "index"])->name('calender');
        Route::post("block-date", [CalendarController::class, "block_date"])->name("calendar.block_date");
        Route::get('unblock_date', [CalendarController::class, 'unblock_date'])->name('calendar.unblock_date');
        Route::post('calendar/update-block-date-note', [CalendarController::class, 'updateBlockDateNote'])->name('calendar.update_block_date_note');
        Route::get('download-ics', [CalendarController::class, "downloadIcs"])->name('calendar.download_ics');
    });
});
Route::get("export-bookings-csv", [\App\Http\Controllers\Booking\BookingController::class, "exportCsv"])->name('export_bookings_csv');
Route::middleware("user_role:user,sub_admin")->group(function () {
    Route::delete("report-review-delete/{id}", [DashboardController::class, "report_review_delete"])->name("report_review_delete");
    Route::get("report-review-resolve/{id}", [DashboardController::class, "report_review_resolve"])->name("report_review_resolve");
    Route::get("delete-image-review/{id}", [DashboardController::class, "review_delete_image"])->name("review_delete_image")->middleware("ajax");

    // booking
    Route::post("cancel-booking-admin", [BookingController::class, "cancelBookingAdmin"])->name('cancel_booking_admin');
    // user management
    Route::get('user-management', [WebsiteController::class, "userManagement"])->name('user_management');
    Route::get("user-detail/{id}", [WebsiteController::class, "user_detail"])->name("user_detail")->middleware("ajax");
    Route::post("admin-user-update", [WebsiteController::class, "admin_user_update"])->name("admin_user_update");
    Route::get('service_provider/{id}', [WebsiteController::class, "serviceProvider"])->name('service_provider');
    Route::get('service-provider-active-bookings/{id}', [WebsiteController::class, "service_provider_active_bookings"])->name('service_provider_active_bookings');
    Route::get('customer_detail/{id}', [WebsiteController::class, "customerDetails"])->name('customer_details');
    Route::get('admin-detail/{id}', [WebsiteController::class, "adminDetails"])->name('admin_details');
    Route::get('report', [WebsiteController::class, "report"])->name('report');
    Route::get('resolved-reports/{id}', [DashboardController::class, "reportsResolved"])->name('report.resolved');
    Route::get("user-delete/{id}", [DashboardController::class, "delete_user"]);
    Route::get("customer-active-bookings/{id}", [UsersController::class, "customer_active_bookings"])->name("customer_active_bookings")->middleware("ajax");


    // suspend
    Route::get("unsuspend-user", [SuspendController::class, "unsuspend_user"])->name("unsuspend_user")->middleware("ajax");
    Route::post("customer-suspend", [SuspendController::class, "customer_suspend"])->name("customer_suspend");
    Route::post("suspend-provider", [SuspendController::class, "service_provider"])->name("suspend_provider_post");
    Route::post("suspend-listing", [SuspendController::class, "suspend_listing"])->name("suspend_listing");
    Route::get("service-provider-payment/{id?}/{status?}", [SuspendController::class, "service_provider_payment"])->name("service_provider_payment");
    Route::get("unsuspend-listing/{id}", [SuspendController::class, "unsuspend_listing"])->name("unsuspend_listing")->middleware("ajax");


    // CMS
    Route::get('cms', [CmsController::class, "cms"])->name('cms');
    Route::post('cms-home', [CmsController::class, "cms_home"])->name('cms_home');
    Route::post('cms-about', [CmsController::class, "cms_about"])->name('cms_about');
    Route::post('cms-contact', [CmsController::class, "cms_contact"])->name('cms_contact');
    Route::get('about_cms', [CmsController::class, "aboutCms"])->name('about_cms');
    Route::get("cms-amenities", [CmsController::class, "amenities"])->name("cms_amenities")->middleware("ajax");

    // Route::get('amenity-options/{id}', [CmsController::class, "amenityOptions"])->name('amenity_options');

    // FAQ
    Route::post("add-faq", [FaqController::class, "add_faq"])->name("add_faq");
    Route::post("faq-upload-image", [FaqController::class, "faq_image_upload"])->name("faq_image_upload");
    Route::patch("faq/update/{id?}", [FaqController::class, "update"])->name("faq.update");
    Route::get('faq/edit/{id}', [FaqController::class, "edit"])->name('faq.edit');
    Route::get('faq/create/{ids?}', [FaqController::class, "create"])->name('faq.create');
    Route::post('faq/create/{ids?}', [FaqController::class, "store"])->name('faq.store');
    Route::delete('faq/delete/{id}', [FaqController::class, "delete"])->name('faq.delete');
    // FAQ sort route
    Route::post('faq/sort', [FaqController::class, 'updateSortOrder'])->name('faq.sort');
    Route::get('user-chats/{user_id?}', [DashboardController::class, "user_chats"])->name('user_chats');

    // Contact / Inbox
    Route::get('contact-message', [ContactController::class, "index"])->name('contact_message');
    Route::post("contact-template-update/{id}", [ContactController::class, "template_update"])->name("contact_message.template_update");
    Route::get('contact-view/{id}', [ContactController::class, "show"])->name('contact_message.view');
    Route::get('delete-contact-message/{id}', [ContactController::class, "delete"])->name('contact_message.delete');
    Route::get('resolved-contact-message/{id}', [ContactController::class, "resolve"])->name('contact_message.resolved');
    Route::post('contact-answer-message', [ContactController::class, "contact_reply"])->name('contact_message.answer');

    //Log Viewer
    Route::get('log-viewers', '\Arcanedev\LogViewer\Http\Controllers\LogViewerController@index')->name('log-viewers');
    Route::get('log-viewers/logs', '\Arcanedev\LogViewer\Http\Controllers\LogViewerController@listLogs')->name('log-viewers.logs');
    Route::delete('log-viewers/logs/delete', '\Arcanedev\LogViewer\Http\Controllers\LogViewerController@delete')->name('log-viewers.logs.delete');
    Route::get('log-viewers/logs/{date}', '\Arcanedev\LogViewer\Http\Controllers\LogViewerController@show')->name('log-viewers.logs.show');
    Route::get('log-viewers/logs/{date}/download', '\Arcanedev\LogViewer\Http\Controllers\LogViewerController@download')->name('log-viewers.logs.download');
    Route::get('log-viewers/logs/{date}/{level}', '\Arcanedev\LogViewer\Http\Controllers\LogViewerController@showByLevel')->name('log-viewers.logs.filter');
    Route::get('log-viewers/logs/{date}/{level}/search', '\Arcanedev\LogViewer\Http\Controllers\LogViewerController@search')->name('log-viewers.logs.search');
    Route::get('log-viewers/logcheck', '\Arcanedev\LogViewer\Http\Controllers\LogViewerController@logCheck')->name('log-viewers.logcheck');

    // listing
    Route::get('add-medical', [ListingController::class, "addMedical"])->name('add_medical');
    Route::post('add-medical', [ListingController::class, "add_medical_post"])->name('add_medical_data');
    Route::post("reject-listing", [ListingController::class, "reject_listing"])->name("reject_listing");
    Route::get("approve-listing/{id}", [ListingController::class, "approve_listing"])->name("approve_listing");
    // Route::get("suspend-listing/{id}", [ListingController::class, "suspend_listing"])->name("suspend_listing");

    // email template
    Route::get("mail-setting", [MailSettingController::class, "index"])->name("mail_setting");
    Route::post("mail-setting", [MailSettingController::class, "update"])->name("mail_setting.update");

});

Route::resource('category/category', 'Category\\CategoryController');
Route::resource('listing/listing', 'Listing\\ListingController');
Route::resource('amenities/amenities', 'Amenities\\AmenitiesController');
Route::resource('booking/booking', 'Booking\\BookingController');
Route::resource('review/review', 'Review\\ReviewController');
Route::resource('card/card', 'Card\\CardController');
Route::resource('wallet/wallet', 'Wallet\\WalletController');
Route::resource('withdrawalRequest/withdrawal-request', 'WithdrawalRequest\\WithdrawalRequestController');
Route::post("wallet-scheduler", [App\Http\Controllers\WithdrawalRequest\WithdrawalRequestController::class, "WalletScheduler"])->name('wallet_scheduler');
Route::post("wallet-manual", [App\Http\Controllers\WithdrawalRequest\WithdrawalRequestController::class, "WalletManual"])->name('wallet_manual');
Route::post('update-schedule-status', [App\Http\Controllers\WithdrawalRequest\WithdrawalRequestController::class, 'updateScheduleStatus'])->name('update_schedule_status')->middleware('auth');
Route::resource('status/status', 'Status\\StatusController');
Route::resource('report/report', 'Report\\ReportController');
Route::get('/getData/{id}', [App\Http\Controllers\Report\ReportController::class, 'getData']);
Route::resource('subAdmin/sub-admin', 'SubAdmin\\SubAdminController');
Route::get('google/translate/change', [WebsiteController::class, 'googleTranslateChange'])->name('google.translate.change');
Route::resource('{amenity_id}/amenity-option', 'AmenityOption\\AmenityOptionController');
Route::resource('helpCenter/help-center', 'HelpCenter\\HelpCenterController');
Route::resource('emailTemplate/email-template', 'EmailTemplate\\EmailTemplateController');

Route::resource('{category_id}/listing-type', 'ListingType\\ListingTypeController');
Route::get('listing-cms', [DashboardController::class, 'listingCmsIndex']);
Route::get('listing-cms/accommodation/edit', [DashboardController::class, 'listingCmsCreateAcc']);
Route::get('listing-cms/vehicle/edit', [DashboardController::class, 'listingCmsCreateVehicle']);
Route::get('listing-cms/watercraft/edit', [DashboardController::class, 'listingCmsCreateWC']);
Route::get('listing-cms/experience/edit', [DashboardController::class, 'listingCmsCreateExp']);
Route::post("listing-cms-update/{category_id}", [CmsController::class, "listing_cms_update"])->name("listing_cms_update");

Route::post('help-center-image', [HelpCenterController::class, 'helpCenterImage'])->name('help_center_image');
Route::post('/help-center-image/delete', [HelpCenterController::class, 'deleteHelpCenterImage'])->name('help-center-image.delete');

Route::get("view-help-center/{id}", [HelpCenterController::class, "viewHelpCenter"])->name("view_help_center");
// Help Center duplicate route
Route::get('helpCenter/help-center/{id}/duplicate', 'HelpCenter\HelpCenterController@duplicate')->name('help-center.duplicate');
Route::get("faq/duplicate/{id}", [FaqController::class, "faq_duplicate"])->name("faq.duplicate");
Route::post('help-center/sort', [HelpCenterController::class, 'updateSortOrder'])->name('help-center.sort');