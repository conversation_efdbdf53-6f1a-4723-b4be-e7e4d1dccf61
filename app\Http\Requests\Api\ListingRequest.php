<?php

namespace App\Http\Requests\Api;

use App\Category;
use App\Listing;
use App\listingGallery;
use Illuminate\Foundation\Http\FormRequest;

class ListingRequest extends FormRequest
{
    private const CATEGORY_METHODS = [
        1 => 'tour',
        2 => 'boat',
        3 => 'car',
        4 => 'house'
    ];

    private const MIN_REQUIRED_IMAGES = 5;

    private const TOUR_MAX_STEP = 28;
    private const BOAT_MAX_STEP = 25;
    private const CAR_MAX_STEP = 27;
    private const HOUSE_MAX_STEP = 24;


    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->user()->hasRole(['service', "user"]);
    }

    public function isApi(): bool
    {
        return $this->is('api/*');
    }

    function getCategory(){
        return Category::where('ids', $this->route('category_ids'))->firstOrFail();
    }
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $step = $this->input('current_step', 1) ;
        $step = $this->isApi() ?  $step : $step - 1;

        // check if request is from web or api
        $category = $this->getCategory();
        $baseRules = ['current_step' => 'required|integer'];
        $rules = array_merge($baseRules, $this->getValidationByCategory($category->id, $step));
        return $rules;
    }

    /**
     * Get validation rules based on category and step
     */
    private function getValidationByCategory(int $categoryId, int $stepNo): array
    {
        if (!isset(self::CATEGORY_METHODS[$categoryId])) {
            return [];
        }

        $method = self::CATEGORY_METHODS[$categoryId];
        return $this->{$method}($stepNo);
    }

    /**
     * Get listing from route parameter
     */
    private function getListingFromRoute(): ?Listing
    {
        $listingIds = $this->route('listing_ids');
        $query = Listing::where('ids', $listingIds);
        if (!auth()->user()->hasRole('user')) {
            $query->where('user_id', auth()->id());
        }
        return $query->first();
        // return dd(Listing::where('ids', $listingIds)->where("user_id", auth()->id())->first());
    }

    /**
     * Check if listing has required number of images
     */
    private function hasRequiredImages(Listing $listing): bool
    {
        return listingGallery::where('listing_id', $listing->id)
            ->where('type', 'image')
            ->count() >= self::MIN_REQUIRED_IMAGES;
    }

    /**
     * Check if listing has document
     */
    private function hasDocument(Listing $listing): bool
    {
        return listingGallery::where('listing_id', $listing->id)
            ->where('type', 'file')
            ->exists();
    }

    /**
     * Check if all validation steps are complete for the listing
     */
    public function isValidationComplete(): bool
    {
        $step = $this->input('current_step', 1);
        $categoryIds = $this->route('category_ids');
        $category = Category::where('ids', $categoryIds)->firstOrFail();

        // Get max steps for category
        $maxSteps = $this->getMaxStepsForCategory($category->id);

        // Check if we've reached the final step
        if ($step < $maxSteps) {
            return false;
        }

        // Check if listing exists and has required media
        $listing = $this->getListingFromRoute();
        if (!$listing) {
            return false;
        }

        // Check required images
        if (!$this->hasRequiredImages($listing)) {
            return false;
        }

        // Check required documents for specific categories
        if ($this->requiresDocument($category->id) && !$this->hasDocument($listing)) {
            return false;
        }

        return true;
    }

    /**
     * Get max steps for each category
     */
    private function getMaxStepsForCategory(int $categoryId): int
    {
        return match($categoryId) {
            1 => self::TOUR_MAX_STEP, // tour
            2 => self::BOAT_MAX_STEP, // boat
            3 => self::CAR_MAX_STEP, // car
            4 => self::HOUSE_MAX_STEP, // house
            default => 29
        };
    }

    /**
     * Check if category requires document
     */
    private function requiresDocument(int $categoryId): bool
    {
        return in_array($categoryId, [2, 3, 4]); // boat, car, house
    }

    /**
     * Get the appropriate listing status based on validation and current status
     */
    public function getListingStatus(): int
    {
        // Get current listing status if updating
        $currentStatus = null;
        $listing = $this->getListingFromRoute();
        if(!$listing){
            return 6;
        }
        if ($listing) {
            $currentStatus = $listing->status;
        }

        // Simple status rules:
        // - If already pending (0), stay pending (cannot go back to draft)
        // - If validation complete, set to pending (0)
        // - If validation incomplete, set to draft (6)

        if ($currentStatus == 0) {
            return 0; // Keep pending status
        }

        return $this->isValidationComplete() ? 0 : 6;
    }

    /**
     * Get media validation rules
     */
    private function getMediaRules(bool $required = true): array
    {
        $rule = $required ? 'required|array' : 'nullable|array';
        return [
            'media' => $rule,
            'media.*' => 'image|mimes:jpeg,png,jpg,heic,heif|max:10240',
        ];
    }

    /**
     * Get document validation rules
     */
    private function getDocumentRules(bool $required = true): array
    {
        $rule = $required ? 'required|file|mimes:pdf,jpeg,png,jpg|max:10240' : 'nullable|file|mimes:pdf,jpeg,png,jpg|max:10240';
        return ['document' => $rule];
    }

    /**
     * Get common field validation rules
     */
    private function getFieldRules(): array
    {
        return [
            'type_id' => ['type_id' => 'required|exists:listing_types,id'],
            'location' => [
                'lat' => 'required|numeric',
                'lng' => 'required|numeric',
                'address' => 'required',
            ],
            'address_details' => [
                'country' => 'required',
                'street' => 'required',
                'suit' => 'nullable',
                'city' => 'required',
                'state' => 'required',
                'zip_code' => 'required',
            ],
            'basic_info' => [
                'name' => 'required|string|max:255',
                'description' => 'required|string|max:5000',
            ],
            'pricing' => [
                'price' => 'required|numeric|min:1',
            ],
            'amenities' => [
                'amenities' => 'nullable|array',
                'amenities.*' => 'exists:amenity_options,id',
            ],
            'key_features' => [
                'key_features' => 'nullable|array',
                'key_features.*.title' => 'required|string|max:255',
                'key_features.*.description' => 'required|string|max:5000',
            ],
            'includes' => [
                'includes' => 'nullable|array',
                'includes.*' => 'string|max:255',
                'not_includes' => 'nullable|array',
                'not_includes.*' => 'string|max:255',
            ],
            'rules' => [
                'pet' => 'required|in:yes,no',
                'allow_rules' => 'nullable|array',
                'allow_rules.*' => 'string|max:255',
                'not_allow_rules' => 'nullable|array',
                'not_allow_rules.*' => 'string|max:255',
            ],
            'notes' => [
                'notes' => 'nullable|array',
                'notes.*' => 'string|max:255',
            ],
            'name' => [
                'name' => 'required|string|max:255',
            ],
            'description' => [
                'description' => 'required|string|max:5000',
            ],
            'cancellation' => [
                'cancellation_policy' => 'required|in:Moderate,Strict,Flexible',
            ],
            'availability' => [
                'listing_availability_period' => 'required|numeric|in:1,3,6',
            ],
            'advance_booking' => [
                'advance_booking_period' => 'required|numeric|in:0,1,2',
            ],
            'minimum_stay' => [
                'minimum_stay_length' => 'required|numeric|in:1,2,3,4,5',
            ],
            'booking_time' => [
                'booking_close_time' => 'required|date_format:g:i A',
            ],
            'seasons' => [
                'seasons' => 'nullable|array',
                // 02/13/2025
                'seasons.*.start_date' => 'nullable|date_format:m/d/Y',
                'seasons.*.end_date' => 'nullable|date_format:m/d/Y',
                'seasons.*.type' => 'nullable|in:Increase,Decrease',
                'seasons.*.percentage' => 'nullable|numeric|min:1|max:100',
            ],
            'new_listing_discount' => [
                'new_listing_discount' => 'nullable|numeric|min:1|max:100',
            ],
            'discounts' => [
                'new_listing_discount' => 'nullable|numeric|min:1|max:100',
                'weekly_discount' => 'nullable|numeric|min:1|max:100',
                'monthly_discount' => 'nullable|numeric|min:1|max:100',
            ],
        ];
    }
    /**
     * Get validation rules for tour category
     */
    private function tour(int $stepNo): array
    {
        $fieldRules = $this->getFieldRules();
        $maxStep = self::TOUR_MAX_STEP;

        $stepRules = [
            0 => [], // Ownership Step
            1 => [], // Introduction Step
            2 => $fieldRules['type_id'],
            3 => $fieldRules['location'],
            4 => $fieldRules['address_details'],
            5 => [
                'booking_capacity' => 'required|numeric|integer|min:1|max:20',
                'private_booking' => 'required|in:yes,no',
                // 'private_booking_price' => 'required_if:private_booking,yes|numeric|min:1',
                'child_allow' => 'required|in:yes,no',
                'child_age_to' => 'required_if:child_allow,yes|nullable|numeric|min:1|max:18',
                'child_age_from' => 'required_if:child_allow,yes|nullable|numeric|min:1|max:18',
            ],
            6 => [
                'tour_day_type' => 'required|in:same_day,multiple_days',
                'start_time' => 'required_if:tour_day_type,same_day',
                'end_time' => 'required_if:tour_day_type,same_day',
                'tour_durations' => 'required_if:tour_day_type,multiple_days|array',
                'tour_durations.*.start_time' => 'required_if:tour_day_type,multiple_days',
                'tour_durations.*.end_time' => 'required_if:tour_day_type,multiple_days',
            ],
            7 => [
                'languages' => 'required|array',
                'languages.*' => 'exists:tour_languages,id',
            ],
            8 => [], // Step 2 introduction
            9 => array_merge($fieldRules['amenities'], ['amenities' => 'required|array']),
            10 => [
                'itinerary' => 'nullable|array',
                'itinerary.*.title' => 'nullable|string|max:255',
                'itinerary.*.description' => 'nullable|string|max:5000',
            ],
            11 => $fieldRules['key_features'],
            12 => $fieldRules['includes'],
            13 => $fieldRules['rules'],
            14 => $fieldRules['notes'],
            15 => [], // Image step
            16 => [], // Document step
            17 => $fieldRules['name'],
            18 => $fieldRules['description'],
            19 => [], // Step 3 introduction
            20 => ['adult_price' => 'required|numeric|min:1'],
            21 => ['child_price' => 'required_if:child_allow,yes|numeric|min:1'],
            22 => ['private_booking_price' => 'required_if:private_booking,yes|numeric|min:1'],
            23 => $fieldRules['cancellation'],
            24 => $fieldRules['availability'],
            25 => $fieldRules['booking_time'],
            26 => $fieldRules['seasons'],
            27 => $fieldRules['new_listing_discount'],
            28 => [] // Step 4 publish
        ];

        return $this->buildRulesForStep(stepRules: $stepRules, stepNo: $stepNo, maxStep: $maxStep, mediaStep: 15);
    }

    /**
     * Build validation rules for a specific step
     */
    private function buildRulesForStep(array $stepRules, int $stepNo, int $maxStep, int $mediaStep, ?int $documentStep = null): array
    {
        $rules = ['current_step' => "required|integer|min:0|max:{$maxStep}"];

        // Check if listing status is 0 (pending) or 1 (publish) - if so, apply all step validations
        $listing = $this->getListingFromRoute();
        $shouldApplyAllSteps = $listing && in_array($listing->status, [0, 1]);

        // If status is pending or publish, use max step instead of current step
        $effectiveStep = $shouldApplyAllSteps ? $maxStep : $stepNo;

        // Apply cumulative step rules
        foreach ($stepRules as $step => $stepRule) {
            if ($effectiveStep >= $step) {
                $rules = array_merge($rules, $stepRule);
            }
        }

        // Handle media validation
        if ($effectiveStep >= $mediaStep) {
            if (!$listing) {
                $rules['listing_ids'] = 'required|exists:listings,ids';
            } else {
                if (!$this->hasRequiredImages($listing)) {
                    $rules = array_merge($rules, $this->getMediaRules());
                }

                // Handle document validation if specified
                if ($documentStep && $effectiveStep >= $documentStep && !$this->hasDocument($listing)) {
                    $rules = array_merge($rules, $this->getDocumentRules());
                }
            }
        }

        return $rules;
    }

    /**
     * Get validation rules for boat category
     */
    private function boat(int $stepNo): array
    {
        $fieldRules = $this->getFieldRules();
        $maxStep = self::BOAT_MAX_STEP;

        $stepRules = [
            0 => [], // Ownership Step
            1 => [], // Introduction Step
            2 => $fieldRules['type_id'],
            3 => $fieldRules['location'],
            4 => $fieldRules['address_details'],
            5 => [
                'capacity' => 'required|numeric|integer|min:1|max:20',
                'boat_length' => 'required|numeric|integer|min:1|max:50',
                'boat_captain' => 'required|in:yes,no',
            ],
            6 => [], // Step 2 introduction
            7 => $fieldRules['amenities'],
            8 => $fieldRules['key_features'],
            9 => $fieldRules['includes'],
            10 => $fieldRules['rules'],
            11 => $fieldRules['notes'],
            12 => [], // Image
            13 => [], // Document
            14 => $fieldRules['name'],
            15 => $fieldRules['description'],
            16 => [], // Step 3 introduction
            17 => [
                'basis_type' => 'required|in:Hourly,Daily',
                'price' => 'required|numeric|min:1',
            ],
            18 => [
                'hourly_availabilities' => 'required_if:basis_type,Hourly|array',
                'check_in_time' => 'required_if:basis_type,Daily',
                'check_out_time' => 'required_if:basis_type,Daily',
            ],
            19 => $fieldRules['cancellation'],
            20 => $fieldRules['availability'],
            21 => $fieldRules['advance_booking'],
            22 => $fieldRules['booking_time'],
            23 => $fieldRules['seasons'],
            24 => $fieldRules['new_listing_discount'],
            25 => [] // Step 4 publish
        ];

        return $this->buildRulesForStep(stepRules: $stepRules, stepNo: $stepNo, maxStep: $maxStep, mediaStep: 12, documentStep: 13);
    }
    /**
     * Get validation rules for car category
     */
    private function car(int $stepNo): array
    {
        $fieldRules = $this->getFieldRules();
        $maxStep = self::CAR_MAX_STEP;

        $stepRules = [
            0 => [], // Ownership Step
            1 => [], // Introduction Step
            2 => $fieldRules['type_id'],
            3 => $fieldRules['location'],
            4 => $fieldRules['address_details'],
            5 => [
                'seats' => 'required|numeric|integer|min:1|max:20',
                'chauffeur' => 'required|in:yes,no',
            ],
            6 => [], // Step 2 introduction
            7 => [
                // 'engine_type' => 'required|in:Diesel,Electric,Gasoline (Petrol),Hydrogen,Hybrid,No Engine,Other',
                'engine_type' => 'required',
            ],
            8 => [
                // 'transmission' => 'required|in:Manual,Automatic,CVT,DCT,Semi-Automatic,No Transmission,Other',
                'transmission' => 'required',
            ],
            9 => $fieldRules['key_features'],
            10 => $fieldRules['includes'],
            11 => $fieldRules['rules'],
            12 => $fieldRules['notes'],
            13 => [], // Image
            14 => [], // Document
            15 => $fieldRules['name'],
            16 => $fieldRules['description'],
            17 => [], // Step 3 introduction
            18 => [
                'basis_type' => 'required|in:Hourly,Daily',
                'price' => 'required|numeric|min:1',
            ],
            19 => [
                'hourly_availabilities' => 'required_if:basis_type,Hourly|array',
                // 'check_in_time' => 'required_if:basis_type,Daily',
                // 'check_out_time' => 'required_if:basis_type,Daily',
            ],
            20 => [
                'placa_restriction' => 'required|in:yes,no',
                'placa_restriction_days' => 'required_if:placa_restriction,yes|array',
                // 'placa_restriction_days.*' => 'in:Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday',
            ],
            21 => $fieldRules['cancellation'],
            22 => $fieldRules['availability'],
            23 => $fieldRules['advance_booking'],
            24 => $fieldRules['booking_time'],
            25 => $fieldRules['seasons'],
            26 => $fieldRules['new_listing_discount'],
            27 => [] // Step 4 publish
        ];

        return $this->buildRulesForStep(stepRules: $stepRules, stepNo: $stepNo, maxStep: $maxStep, mediaStep: 13, documentStep: 14);
    }
    /**
     * Get validation rules for house category
     */
    private function house(int $stepNo): array
    {
        $fieldRules = $this->getFieldRules();
        $maxStep = self::HOUSE_MAX_STEP;

        $stepRules = [
            0 => [], // Ownership Step
            1 => [], // Introduction Step
            2 => $fieldRules['type_id'],
            3 => $fieldRules['location'],
            4 => $fieldRules['address_details'],
            5 => [
                'guests' => 'required|numeric|integer|min:1|max:20',
                'bedrooms' => 'required|numeric|integer|min:1|max:20',
                'bathrooms' => 'required|numeric|integer|min:1|max:20',
            ],
            6 => [], // Step 2 introduction
            7 => $fieldRules['amenities'],
            8 => $fieldRules['key_features'],
            9 => $fieldRules['rules'],
            10 => $fieldRules['notes'],
            11 => [], // Image
            12 => [], // Document
            13 => $fieldRules['name'],
            14 => $fieldRules['description'],
            15 => [], // Step 3 introduction
            16 => $fieldRules['pricing'],
            17 => $fieldRules['cancellation'],
            18 => $fieldRules['minimum_stay'],
            19 => $fieldRules['availability'],
            20 => $fieldRules['advance_booking'],
            21 => [
                'check_in_time' => 'required|date_format:g:i A',
                'check_out_time' => 'required|date_format:g:i A',
                'booking_close_time' => $fieldRules['booking_time']['booking_close_time'],
            ],
            22 => $fieldRules['seasons'],
            23 => $fieldRules['discounts'],
            24 => [] // Step 4 publish
        ];

        return $this->buildRulesForStep(stepRules: $stepRules, stepNo: $stepNo, maxStep: $maxStep, mediaStep: 11, documentStep: 12);
    }

    // custom message
    public function messages()
    {
        return [
            'listing_ids.exists' => 'The listing does not exist.',
            "media.required" => "The media file is required minimum 5 images.",
        ];
    }
    public function validated(){
        $validated = parent::validated();
        $validated["category_id"] = $this->getCategory()->id;
        $validated["listing_id"] = $this->getListingFromRoute()->ids ?? null;
        $validated["user_id"] = auth()->user()->id ?? null;
        $validated["listing_status"] = $this->getListingStatus();
        return $validated;
    }
}
