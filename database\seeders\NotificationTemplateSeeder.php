<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\NotificationTemplate;
use App\Models\NotificationTemplateTranslation;

class NotificationTemplateSeeder extends Seeder
{
    public function run()
    {
        $templates = [
            [
                'key' => 'booking_placed',
                'translations' => [
                    'en' => [
                        'title' => 'Booking Placed',
                        'message' => ':customer_name booked the :listing_name on :check_in_date'
                    ],
                    'es' => [
                        'title' => 'Reserva Realizada',
                        'message' => ':customer_name reservó :listing_name para el :check_in_date'
                    ]
                ]
            ],
            [
                'key' => 'booking_cancelled',
                'translations' => [
                    'en' => [
                        'title' => 'Booking Cancelled',
                        'message' => ':customer_name cancelled booking for :listing_name on :check_in_date'
                    ],
                    'es' => [
                        'title' => 'Reserva Cancelada',
                        'message' => ':customer_name canceló la reserva de :listing_name para el :check_in_date'
                    ]
                ]
            ],
            [
                'key' => 'user_registered',
                'translations' => [
                    'en' => [
                        'title' => 'New User Registered',
                        'message' => 'A new user has registered on the platform'
                    ],
                    'es' => [
                        'title' => 'Nuevo Usuario Registrado',
                        'message' => 'Un nuevo usuario se ha registrado en la plataforma'
                    ]
                ]
            ]
        ];

        foreach ($templates as $templateData) {
            $template = NotificationTemplate::create([
                'key' => $templateData['key'],
                'type' => 'system',
                'is_active' => true
            ]);

            foreach ($templateData['translations'] as $locale => $translation) {
                NotificationTemplateTranslation::create([
                    'notification_template_id' => $template->id,
                    'locale' => $locale,
                    'title' => $translation['title'],
                    'message' => $translation['message']
                ]);
            }
        }
    }
}