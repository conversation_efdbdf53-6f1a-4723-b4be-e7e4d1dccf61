<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class NotificationTemplateTranslations extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
   public function up()
    {
        Schema::create('notification_template_translations', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('notification_template_id');
            $table->string('locale')->index();

            $table->string('title');
            $table->text('message');
            $table->timestamps();
            
            // $table->unique(['notification_template_id', 'locale']);
            // $table->foreign('notification_template_id')->references('id')->on('notification_templates')->onDelete('cascade');
        });
    }

    public function down()
    {
        Schema::dropIfExists('notification_template_translations');
    }
}
